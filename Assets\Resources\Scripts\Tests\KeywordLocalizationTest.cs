using System.Collections;
using System.IO;
using UnityEngine;

/// <summary>
/// Test script for the Keyword Localization System
/// Provides comprehensive testing of keyword loading, retrieval, import functionality, and error handling
/// Can be run via context menu in Unity Editor or attached to a GameObject for runtime testing
/// </summary>
public class KeywordLocalizationTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool runTestsOnStart = false;
    [SerializeField] private bool enableDetailedLogging = true;

    private void Start()
    {
        if (runTestsOnStart)
        {
            StartCoroutine(RunAllTests());
        }
    }

    /// <summary>
    /// Runs all keyword localization tests
    /// Available as context menu item in Unity Editor
    /// </summary>
    [ContextMenu("Run All Keyword Tests")]
    public void RunAllTestsMenu()
    {
        StartCoroutine(RunAllTests());
    }

    /// <summary>
    /// Comprehensive test suite for keyword localization system
    /// </summary>
    private IEnumerator RunAllTests()
    {
        Debug.Log("🧪 [KEYWORD_TEST] Starting Keyword Localization System Tests...");

        yield return new WaitForSeconds(0.1f);

        // Test 1: Basic keyword retrieval
        TestBasicKeywordRetrieval();
        yield return new WaitForSeconds(0.1f);

        // Test 2: Error handling for missing keys
        TestMissingKeyHandling();
        yield return new WaitForSeconds(0.1f);

        // Test 3: Keyword existence checking
        TestKeywordExistence();
        yield return new WaitForSeconds(0.1f);

        // Test 4: Advanced keyword retrieval
        TestAdvancedKeywordRetrieval();
        yield return new WaitForSeconds(0.1f);

        // Test 5: System status and debugging
        TestSystemStatus();
        yield return new WaitForSeconds(0.1f);

        // Test 6: Default value functionality
        TestDefaultValueFunctionality();
        yield return new WaitForSeconds(0.1f);

        // Test 7: Import simulation (create test file)
        TestImportSimulation();
        yield return new WaitForSeconds(0.1f);

        // Test 8: Performance testing
        TestPerformance();
        yield return new WaitForSeconds(0.1f);

        // Test 9: Modifier functionality
        TestModifierFunctionality();
        yield return new WaitForSeconds(0.1f);

        // Test 10: Modifier convenience methods
        TestModifierConvenienceMethods();

        Debug.Log("✅ [KEYWORD_TEST] All Keyword and Modifier Localization System Tests Completed!");
    }

    /// <summary>
    /// Test basic keyword retrieval functionality
    /// </summary>
    private void TestBasicKeywordRetrieval()
    {
        Debug.Log("🔍 [TEST_1] Testing Basic Keyword Retrieval...");

        // Test retrieving default keywords
        string acceptText = KeywordManager.GetWord("GENERIC_ACCEPT");
        string cancelText = KeywordManager.GetWord("GENERIC_CANCEL");
        string menuText = KeywordManager.GetWord("MENU_MAIN");

        LogTest("GENERIC_ACCEPT", acceptText, "Confirmar");
        LogTest("GENERIC_CANCEL", cancelText, "Cancelar");
        LogTest("MENU_MAIN", menuText, "Menu Principal");

        Debug.Log($"✅ [TEST_1] Basic keyword retrieval test completed");
    }

    /// <summary>
    /// Test error handling for missing keys
    /// </summary>
    private void TestMissingKeyHandling()
    {
        Debug.Log("🔍 [TEST_2] Testing Missing Key Handling...");

        // Test missing key - should return the key itself as fallback
        string missingKey = KeywordManager.GetWord("NONEXISTENT_KEY");
        LogTest("NONEXISTENT_KEY (missing)", missingKey, "NONEXISTENT_KEY");

        // Test empty key
        string emptyKey = KeywordManager.GetWord("");
        LogTest("Empty key", emptyKey, "");

        // Test null key
        string nullKey = KeywordManager.GetWord(null);
        LogTest("Null key", nullKey, "");

        Debug.Log($"✅ [TEST_2] Missing key handling test completed");
    }

    /// <summary>
    /// Test keyword existence checking
    /// </summary>
    private void TestKeywordExistence()
    {
        Debug.Log("🔍 [TEST_3] Testing Keyword Existence Checking...");

        // Test existing keys
        bool acceptExists = KeywordManager.HasKey("GENERIC_ACCEPT");
        bool cancelExists = KeywordManager.HasKey("GENERIC_CANCEL");

        // Test non-existing key
        bool missingExists = KeywordManager.HasKey("NONEXISTENT_KEY");

        // Test edge cases
        bool emptyExists = KeywordManager.HasKey("");
        bool nullExists = KeywordManager.HasKey(null);

        LogTest("GENERIC_ACCEPT exists", acceptExists.ToString(), "True");
        LogTest("GENERIC_CANCEL exists", cancelExists.ToString(), "True");
        LogTest("NONEXISTENT_KEY exists", missingExists.ToString(), "False");
        LogTest("Empty key exists", emptyExists.ToString(), "False");
        LogTest("Null key exists", nullExists.ToString(), "False");

        Debug.Log($"✅ [TEST_3] Keyword existence checking test completed");
    }

    /// <summary>
    /// Test advanced keyword retrieval features
    /// </summary>
    private void TestAdvancedKeywordRetrieval()
    {
        Debug.Log("🔍 [TEST_4] Testing Advanced Keyword Retrieval...");

        // Test getting complete keyword object
        JsonKeyword keyword = KeywordManager.GetKeyword("GENERIC_ACCEPT");
        if (keyword != null)
        {
            LogTest("Keyword object ID", keyword.id, "cw100");
            LogTest("Keyword object key", keyword.key, "GENERIC_ACCEPT");
            LogTest("Keyword object word", keyword.word, "Confirmar");
            LogTest("Keyword object tags", keyword.keywordTags?.Length.ToString() ?? "0", "1");
        }
        else
        {
            Debug.LogError("❌ [TEST_4] Failed to retrieve keyword object for GENERIC_ACCEPT");
        }

        // Test getting keyword object for missing key
        JsonKeyword missingKeyword = KeywordManager.GetKeyword("NONEXISTENT_KEY");
        LogTest("Missing keyword object", (missingKeyword == null).ToString(), "True");

        Debug.Log($"✅ [TEST_4] Advanced keyword retrieval test completed");
    }

    /// <summary>
    /// Test system status and debugging features
    /// </summary>
    private void TestSystemStatus()
    {
        Debug.Log("🔍 [TEST_5] Testing System Status and Debugging...");

        // Test system status
        bool isLoaded = KeywordManager.Instance.IsLoaded();
        LogTest("System loaded", isLoaded.ToString(), "True");

        // Test keyword count
        int keywordCount = KeywordManager.GetKeywordCount();
        LogTest("Keyword count", keywordCount.ToString(), "20"); // Default keywords

        // Test getting all keys
        string[] allKeys = KeywordManager.GetAllKeys();
        LogTest("All keys count", allKeys.Length.ToString(), "20");

        if (enableDetailedLogging)
        {
            Debug.Log($"📋 [TEST_5] All available keys: {string.Join(", ", allKeys)}");
        }

        Debug.Log($"✅ [TEST_5] System status and debugging test completed");
    }

    /// <summary>
    /// Test default value functionality
    /// </summary>
    private void TestDefaultValueFunctionality()
    {
        Debug.Log("🔍 [TEST_6] Testing Default Value Functionality...");

        // Test existing key with default
        string existingWithDefault = KeywordManager.GetWordOrDefault("GENERIC_ACCEPT", "Default Value");
        LogTest("Existing key with default", existingWithDefault, "Confirmar");

        // Test missing key with default
        string missingWithDefault = KeywordManager.GetWordOrDefault("NONEXISTENT_KEY", "Default Value");
        LogTest("Missing key with default", missingWithDefault, "Default Value");

        // Test missing key with null default
        string missingWithNullDefault = KeywordManager.GetWordOrDefault("NONEXISTENT_KEY", null);
        LogTest("Missing key with null default", missingWithNullDefault, "");

        // Test empty key with default
        string emptyWithDefault = KeywordManager.GetWordOrDefault("", "Default Value");
        LogTest("Empty key with default", emptyWithDefault, "Default Value");

        Debug.Log($"✅ [TEST_6] Default value functionality test completed");
    }

    /// <summary>
    /// Test import simulation by creating a test file
    /// </summary>
    private void TestImportSimulation()
    {
        Debug.Log("🔍 [TEST_7] Testing Import Simulation...");

        try
        {
            // Create test import file with correct nested structure including both keywords and modifiers
            string testImportData = @"{
  ""keywordPkg"": {
    ""exportedDateTime"": ""01/08/2025_14:30:00"",
    ""appVersion"": ""9.10.54"",
    ""data"": [
      {
        ""id"": ""test001"",
        ""key"": ""TEST_KEYWORD_1"",
        ""word"": ""Teste 1"",
        ""keywordTags"": [""TEST""],
        ""revisionCounterWordAI"": 1
      },
      {
        ""id"": ""test002"",
        ""key"": ""TEST_KEYWORD_2"",
        ""word"": ""Teste 2"",
        ""keywordTags"": [""TEST"", ""SAMPLE""],
        ""revisionCounterWordAI"": 2
      }
    ]
  },
  ""modifierListPkg"": {
    ""exportedDateTime"": ""01/08/2025_14:30:00"",
    ""appVersion"": ""9.10.54"",
    ""data"": [
      {
        ""id"": ""TEST_MO1"",
        ""skill"": ""Teste Habilidade 1"",
        ""description"": ""Descrição do teste de habilidade 1"",
        ""acronym"": ""Th1"",
        ""revisionCounterSkillAI"": 1,
        ""revisionCounterDescriptionAI"": 1
      },
      {
        ""id"": ""TEST_MO2"",
        ""skill"": ""Teste Habilidade 2"",
        ""description"": ""Descrição do teste de habilidade 2"",
        ""acronym"": ""Th2"",
        ""revisionCounterSkillAI"": 2,
        ""isReviewedDescription"": true
      }
    ]
  }
}";

            string testFilePath = Path.Combine(Application.temporaryCachePath, "test_keywords.json");
            File.WriteAllText(testFilePath, testImportData);

            Debug.Log($"📁 [TEST_7] Created test import file at: {testFilePath}");
            Debug.Log($"📋 [TEST_7] Test file content preview: {testImportData.Substring(0, Mathf.Min(100, testImportData.Length))}...");

            // Note: Actual import testing would require UI interaction or direct method calls
            // This test just verifies file creation for manual import testing

        }
        catch (System.Exception ex)
        {
            Debug.LogError($"❌ [TEST_7] Failed to create test import file: {ex.Message}");
        }

        Debug.Log($"✅ [TEST_7] Import simulation test completed");
    }

    /// <summary>
    /// Test performance of keyword retrieval
    /// </summary>
    private void TestPerformance()
    {
        Debug.Log("🔍 [TEST_8] Testing Performance...");

        const int iterations = 1000;
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Test repeated keyword retrieval
        for (int i = 0; i < iterations; i++)
        {
            KeywordManager.GetWord("GENERIC_ACCEPT");
            KeywordManager.GetWord("GENERIC_CANCEL");
            KeywordManager.GetWord("MENU_MAIN");
            KeywordManager.HasKey("GENERIC_ACCEPT");
        }

        stopwatch.Stop();

        float averageTimeMs = (float)stopwatch.ElapsedMilliseconds / iterations;
        Debug.Log($"⚡ [TEST_8] Performance test: {iterations} iterations in {stopwatch.ElapsedMilliseconds}ms");
        Debug.Log($"⚡ [TEST_8] Average time per operation: {averageTimeMs:F4}ms");

        // Performance should be very fast (< 0.1ms per operation)
        if (averageTimeMs < 0.1f)
        {
            Debug.Log($"✅ [TEST_8] Performance test PASSED - Excellent performance");
        }
        else if (averageTimeMs < 1.0f)
        {
            Debug.Log($"⚠️ [TEST_8] Performance test WARNING - Acceptable but could be optimized");
        }
        else
        {
            Debug.LogWarning($"❌ [TEST_8] Performance test FAILED - Performance issues detected");
        }

        Debug.Log($"✅ [TEST_8] Performance test completed");
    }

    /// <summary>
    /// Test modifier functionality
    /// </summary>
    private void TestModifierFunctionality()
    {
        Debug.Log("🔍 [TEST_9] Testing Modifier Functionality...");

        // Test retrieving default modifiers
        string knowledgeSkill = KeywordManager.GetModifierSkill("MO0");
        string luckSkill = KeywordManager.GetModifierSkill("MO1");
        string speedSkill = KeywordManager.GetModifierSkill("MO2");

        LogTest("Knowledge skill", knowledgeSkill, "Inteligência");
        LogTest("Luck skill", luckSkill, "Sorte");
        LogTest("Speed skill", speedSkill, "Velocidade");

        // Test modifier acronyms
        string knowledgeAcronym = KeywordManager.GetModifierAcronym("MO0");
        string luckAcronym = KeywordManager.GetModifierAcronym("MO1");

        LogTest("Knowledge acronym", knowledgeAcronym, "Qi");
        LogTest("Luck acronym", luckAcronym, "So");

        // Test modifier descriptions
        string knowledgeDesc = KeywordManager.GetModifierDescription("MO0");
        bool hasDescription = !string.IsNullOrEmpty(knowledgeDesc);
        LogTest("Knowledge has description", hasDescription.ToString(), "True");

        // Test missing modifier
        string missingModifier = KeywordManager.GetModifierSkill("MO99");
        LogTest("Missing modifier fallback", missingModifier, "MO99");

        // Test modifier count
        int modifierCount = KeywordManager.GetModifierCount();
        LogTest("Modifier count", modifierCount.ToString(), "6");

        // Test getting all modifier IDs
        string[] allModifierIds = KeywordManager.GetAllModifierIds();
        LogTest("All modifier IDs count", allModifierIds.Length.ToString(), "6");

        if (enableDetailedLogging)
        {
            Debug.Log($"📋 [TEST_9] All modifier IDs: {string.Join(", ", allModifierIds)}");
        }

        Debug.Log($"✅ [TEST_9] Modifier functionality test completed");
    }

    /// <summary>
    /// Test modifier convenience methods
    /// </summary>
    private void TestModifierConvenienceMethods()
    {
        Debug.Log("🔍 [TEST_10] Testing Modifier Convenience Methods...");

        // Test skill name convenience methods
        string knowledge = KeywordManager.GetKnowledgeSkillName();
        string luck = KeywordManager.GetLuckSkillName();
        string speed = KeywordManager.GetSpeedSkillName();
        string precision = KeywordManager.GetPrecisionSkillName();
        string evasion = KeywordManager.GetEvasionSkillName();
        string critical = KeywordManager.GetCriticalChanceSkillName();

        LogTest("Knowledge skill convenience method", knowledge, "Inteligência");
        LogTest("Luck skill convenience method", luck, "Sorte");
        LogTest("Speed skill convenience method", speed, "Velocidade");
        LogTest("Precision skill convenience method", precision, "Precisão");
        LogTest("Evasion skill convenience method", evasion, "Evasão");
        LogTest("Critical skill convenience method", critical, "Chance Crítica");

        // Test acronym convenience methods
        string knowledgeAcronym = KeywordManager.GetKnowledgeAcronym();
        string luckAcronym = KeywordManager.GetLuckAcronym();
        string speedAcronym = KeywordManager.GetSpeedAcronym();
        string precisionAcronym = KeywordManager.GetPrecisionAcronym();
        string evasionAcronym = KeywordManager.GetEvasionAcronym();
        string criticalAcronym = KeywordManager.GetCriticalChanceAcronym();

        LogTest("Knowledge acronym convenience method", knowledgeAcronym, "Qi");
        LogTest("Luck acronym convenience method", luckAcronym, "So");
        LogTest("Speed acronym convenience method", speedAcronym, "Ve");
        LogTest("Precision acronym convenience method", precisionAcronym, "Pr");
        LogTest("Evasion acronym convenience method", evasionAcronym, "Ev");
        LogTest("Critical acronym convenience method", criticalAcronym, "Cc");

        // Test description convenience methods
        string knowledgeDesc = KeywordManager.GetKnowledgeDescription();
        string luckDesc = KeywordManager.GetLuckDescription();
        string speedDesc = KeywordManager.GetSpeedDescription();
        string precisionDesc = KeywordManager.GetPrecisionDescription();
        string evasionDesc = KeywordManager.GetEvasionDescription();
        string criticalDesc = KeywordManager.GetCriticalChanceDescription();

        LogTest("Knowledge description not empty", (!string.IsNullOrEmpty(knowledgeDesc)).ToString(), "True");
        LogTest("Luck description not empty", (!string.IsNullOrEmpty(luckDesc)).ToString(), "True");
        LogTest("Speed description not empty", (!string.IsNullOrEmpty(speedDesc)).ToString(), "True");
        LogTest("Precision description not empty", (!string.IsNullOrEmpty(precisionDesc)).ToString(), "True");
        LogTest("Evasion description not empty", (!string.IsNullOrEmpty(evasionDesc)).ToString(), "True");
        LogTest("Critical description not empty", (!string.IsNullOrEmpty(criticalDesc)).ToString(), "True");

        if (enableDetailedLogging)
        {
            Debug.Log($"📋 [TEST_10] Knowledge description: {knowledgeDesc}");
            Debug.Log($"📋 [TEST_10] Luck description: {luckDesc}");
            Debug.Log($"📋 [TEST_10] Speed description: {speedDesc}");
        }

        // Test getting complete modifier objects
        JsonModifier knowledgeModifier = KeywordManager.GetModifier("MO0");
        if (knowledgeModifier != null)
        {
            LogTest("Knowledge modifier ID", knowledgeModifier.id, "MO0");
            LogTest("Knowledge modifier skill", knowledgeModifier.skill, "Inteligência");
            LogTest("Knowledge modifier acronym", knowledgeModifier.acronym, "Qi");
            LogTest("Knowledge modifier has description", (!string.IsNullOrEmpty(knowledgeModifier.description)).ToString(), "True");
        }
        else
        {
            Debug.LogError("❌ [TEST_10] Failed to retrieve knowledge modifier object");
        }

        // Test missing modifier object
        JsonModifier missingModifier = KeywordManager.GetModifier("MO99");
        LogTest("Missing modifier object", (missingModifier == null).ToString(), "True");

        Debug.Log($"✅ [TEST_10] Modifier convenience methods test completed");
    }

    /// <summary>
    /// Helper method to log test results with consistent formatting
    /// </summary>
    private void LogTest(string testName, string actual, string expected)
    {
        bool passed = actual == expected;
        string status = passed ? "✅ PASS" : "❌ FAIL";
        string message = $"{status} [{testName}] Expected: '{expected}', Actual: '{actual}'";

        if (passed)
        {
            if (enableDetailedLogging) Debug.Log(message);
        }
        else
        {
            Debug.LogError(message);
        }
    }

    /// <summary>
    /// Manual test for reload functionality
    /// </summary>
    [ContextMenu("Test Keyword Reload")]
    public void TestKeywordReload()
    {
        Debug.Log("🔄 [RELOAD_TEST] Testing keyword reload functionality...");

        int beforeCount = KeywordManager.GetKeywordCount();
        Debug.Log($"📊 [RELOAD_TEST] Keywords before reload: {beforeCount}");

        KeywordManager.ReloadKeywords();

        int afterCount = KeywordManager.GetKeywordCount();
        Debug.Log($"📊 [RELOAD_TEST] Keywords after reload: {afterCount}");

        bool reloadSuccessful = afterCount > 0;
        Debug.Log($"{(reloadSuccessful ? "✅" : "❌")} [RELOAD_TEST] Reload {(reloadSuccessful ? "successful" : "failed")}");
    }
}
