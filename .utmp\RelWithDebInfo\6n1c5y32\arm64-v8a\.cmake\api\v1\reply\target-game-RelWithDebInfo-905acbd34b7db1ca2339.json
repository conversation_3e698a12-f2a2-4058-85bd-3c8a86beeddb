{"artifacts": [{"path": "D:/Menino <PERSON>/NEW-DKG-RPG_mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/6n1c5y32/obj/arm64-v8a/libgame.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_definitions", "target_include_directories"], "files": ["GameActivity/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 15, "parent": 0}, {"command": 1, "file": 0, "line": 23, "parent": 0}, {"command": 2, "file": 0, "line": 19, "parent": 0}, {"command": 3, "file": 0, "line": 21, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC"}], "defines": [{"backtrace": 3, "define": "EXTERNAL_GAME_ACTIVITY_CODE"}, {"define": "game_EXPORTS"}], "includes": [{"backtrace": 4, "path": "D:/<PERSON>ino <PERSON>/NEW-DKG-RPG_mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include"}], "language": "CXX", "sourceIndexes": [4, 6, 8, 10, 12, 14, 15, 16], "sysroot": {"path": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "game::@d02bb112ea9f9c2ed29f", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--fatal-warnings -Qunused-arguments -Wl,--no-undefined", "role": "flags"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "\"C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4533d298259fc52a43021fce53f5e4a9\\transformed\\jetified-games-activity-3.0.5\\prefab\\modules\\game-activity_static\\libs\\android.arm64-v8a\\libgame-activity_static.a\"", "role": "libraries"}, {"backtrace": 2, "fragment": "-llog", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "game", "nameOnDisk": "libgame.so", "paths": {"build": "GameActivity", "source": "GameActivity"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 5, 7, 9, 11, 13, 17, 18, 19, 20, 21, 22, 23, 24]}, {"name": "Source Files", "sourceIndexes": [4, 6, 8, 10, 12, 14, 15, 16]}], "sources": [{"backtrace": 1, "path": "GameActivity/GAToUnityCallbacks.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/MacroEnd.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/MacroHeaderBegin.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/MacroSourceBegin.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "GameActivity/UGAApplication.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "GameActivity/UGAApplication.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "GameActivity/UGAConfiguration.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "GameActivity/UGAConfiguration.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "GameActivity/UGADebug.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "GameActivity/UGADebug.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "GameActivity/UGAEntry.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "GameActivity/UGAEvents.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "GameActivity/UGAInput.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "GameActivity/UGAInput.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "GameActivity/UGAInputKeyEvent.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "GameActivity/UGAInputMotionEvent.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "GameActivity/UGASoftKeyboard.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "GameActivity/UGASoftKeyboard.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/UGASoftKeyboardCallbacks.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/UGATypes.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/UGAVersion.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/UnityToGACallbacks.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/UnityToGAConfigurationCallbacks.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/UnityToGAKeyEventCallbacks.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "GameActivity/UnityToGAMotionEventCallbacks.h", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}