using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.UI;
using System.Collections;

public class CharatersForPartyUIHandler : MonoBehaviour
{
    ConfigsHandler configsHandler;

    public GameObject partyScrollContent; // the scroll content of the party UI
    ToggleGroup editParty; // Toggle group of the party to edit
    public int selectedParty = 0; // the selected party to edit index

    public RectTransform scrollContent;

    public RectTransform charGridBox; // the grid box that contains the characters
    public ScrollRect scrollRect;
    public CharPartyPool charPartyPool; // the character party pool
    public int totalCharacters;
    public float itemHeight;
    public float itemWidth;
    public float viewportHeight;
    private int columns = 4;
    private float spacing = 3.5f; // Spacing between items, adjust as needed


    private Dictionary<int, GameObject> visibleCharacters = new();
    private List<BattleCharacter> charactersThatAreNotEnemies = new(); // List of all characters that are not enemies

    void Start()
    {
        // gets the ConfigsHandler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        // gets the toggle group of the party to edit
        editParty = partyScrollContent.transform.GetChild(0).GetComponent<ToggleGroup>();


        charactersThatAreNotEnemies = configsHandler.GetCharacters().FindAll(c => !c.isEnemy);
        totalCharacters = charactersThatAreNotEnemies.Count;

        // Calculate itemHeight from prefab
        if (charPartyPool.characterPartyPrefab != null)
        {
            var rect = charPartyPool.characterPartyPrefab.GetComponent<RectTransform>();
            // Account for scale factor in size calculations
            itemHeight = rect.rect.height * rect.localScale.y;
            itemWidth = rect.rect.width * rect.localScale.x;

        }
        else
        {
            Debug.LogError("Character prefab not assigned in CharPool!");
        }

        // Set the scroll content height for virtualization
        int totalRows = Mathf.CeilToInt((float)totalCharacters / columns);
        float fullRowHeight = itemHeight + spacing;

        // Add top margin to prevent first row from being cut off
        float gridHeight = totalRows * fullRowHeight;


        // For stretched anchors, sizeDelta.x represents margins, not width
        // Set the grid height only, let width be determined by parent
        charGridBox.sizeDelta = new Vector2(
            charGridBox.sizeDelta.x, // Keep existing width/margin settings
            gridHeight
        );

        // Calculate actual available width after anchors are applied
        float actualGridWidth = charGridBox.rect.width;
        float minRequiredWidth = columns * itemWidth + (columns - 1) * spacing;



        float staticSectionHeight = scrollContent
            .GetComponentsInChildren<RectTransform>()
            .Where(rt => rt != charGridBox && rt.parent == scrollContent)
            .Sum(rt => rt.sizeDelta.y);

        scrollContent.sizeDelta = new Vector2(
            scrollContent.sizeDelta.x,
            staticSectionHeight + gridHeight
        );

        scrollRect.onValueChanged.AddListener((v) =>
        {
            UpdateVisibleCharacters();
        });

        // Delay the initial update to ensure layout is ready
        StartCoroutine(DelayedInitialUpdate());
    }

    private IEnumerator DelayedInitialUpdate()
        {
            yield return null; // Wait one frame
            UpdateVisibleCharacters();
        }

    void Update()
    {
        // gets all the characters that are not enemies
        //List<BattleCharacter> characters = configsHandler.GetCharacters().FindAll(c => !c.isEnemy);

        // gets the selected party index
        if (editParty == null || editParty.ActiveToggles().Count() == 0)
        {
            selectedParty = 0; // Default to the first party if none are selected
        }
        else
        {
            // gets the index of the selected party
            selectedParty = editParty.ActiveToggles().FirstOrDefault().transform.GetSiblingIndex();
        }
        //selectedParty = editParty.ActiveToggles().FirstOrDefault().transform.GetSiblingIndex();

        // updates the chacaters for the parties if there are new characters
/*         if (GetComponentsInChildren<CharacterValuesParty>().Length != characters.Count)
        {
            foreach (BattleCharacter character in characters.Except(GetComponentsInChildren<CharacterValuesParty>().Select(c => c.character).ToList()))
            {
                // instantiates the character UI
                GameObject characterUI = Instantiate(Resources.Load<GameObject>("Prefabs/CharacterPartyList"), transform.GetChild(0).GetChild(0));
                // sets the name
                characterUI.name = "Char" + characters.IndexOf(character);
                // sets the character
                characterUI.GetComponent<CharacterValuesParty>().character = character;
            }
        } */
/*
        foreach (CharacterValuesParty charParty in GetComponentsInChildren<CharacterValuesParty>()) // loops through all the characters
        {
            if (characters.IndexOf(characters.Find(c => c.id == charParty.character.id)) == -1) // if the character is not in the list destroy it
            {
                Destroy(charParty.gameObject);
                break;
            }
            // disables the button if the character is already in the party
            if (configsHandler.IsCharacterOnParty(charParty.character, selectedParty)) charParty.GetComponent<Button>().interactable = false;
            else charParty.GetComponent<Button>().interactable = true; // otherwise enables the button

        } */
    }

    private void UpdateVisibleCharacters()
    {
        // Calculate viewportHeight from the ScrollRect's viewport
        viewportHeight = scrollRect.viewport.rect.height;
        float fullRowHeight = itemHeight + spacing;



        // Calculate correct scrollY based on normalized position
        float scrollY = (1 - scrollRect.verticalNormalizedPosition) * (scrollContent.rect.height - viewportHeight);

        // Adjust scrollY relative to the start of the charGridBox
        float gridStartY = Mathf.Abs(charGridBox.anchoredPosition.y); // Should be positive if placed downwards
        float localScrollY = scrollY - gridStartY;

        if (localScrollY < 0)
            localScrollY = 0;

        int firstVisibleRow = Mathf.FloorToInt(localScrollY / fullRowHeight);
        int visibleRowCount = Mathf.CeilToInt(viewportHeight / fullRowHeight) + 1;

        int firstVisibleIndex = firstVisibleRow * columns;
        int lastVisibleIndex = Mathf.Min(totalCharacters - 1, (firstVisibleRow + visibleRowCount) * columns - 1);

        // Hide and return pooled objects for characters that are no longer visible
        List<int> toRemove = new(visibleCharacters.Keys);
        foreach (int key in toRemove)
        {
            if (key < firstVisibleIndex || key > lastVisibleIndex)
            {
                charPartyPool.ReturnPartyToPool(visibleCharacters[key]);
                visibleCharacters.Remove(key);
            }
        }

        // Show new visible characters
        for (int i = firstVisibleIndex; i <= lastVisibleIndex; i++)
        {
            if (!visibleCharacters.ContainsKey(i))
            {
                GameObject obj = charPartyPool.GetPooledPartyObject();
                if (obj != null)
                {
                    obj.transform.SetParent(charGridBox, false);
                    var battleChar = GetBattleCharacter(i);
                    obj.GetComponent<CharacterValuesParty>().character = battleChar;
                    //obj.GetComponent<CharacterValuesParty>().UpdateUI();
                    obj.name = "CharParty_" + i + "_" + battleChar.name;
                    obj.SetActive(true);
                    visibleCharacters[i] = obj;
                }
                else
                {
                    Debug.LogWarning("[CharVirtualizationController] No pooled object available!");
                }
            }
            // Set position for all visible objects (new or already active)
            if (visibleCharacters.TryGetValue(i, out var go))
            {
                int row = i / columns;
                int col = i % columns;
                var rect = go.GetComponent<RectTransform>();

                // Calculate how many items are in this row
                int startIndex = row * columns;
                int itemsInRow = Mathf.Min(columns, totalCharacters - startIndex);

                // Calculate total width needed for this row (items + spacing between them)
                float totalSpacingInRow = (itemsInRow - 1) * spacing;
                float rowWidth = itemsInRow * itemWidth + totalSpacingInRow;

                // Center the row within the grid box
                // Calculate the starting X position to center the row
                float availableWidth = charGridBox.rect.width;
                float startX = (availableWidth - rowWidth) / 2f;

                // Position this item within the row
                // Each item is positioned at: startX + (item_index * item_width) + (item_index * spacing)
                float x = startX + col * (itemWidth + spacing);
                // Add top margin to prevent first row from being cut off
                float topMargin = spacing * 2f; // Double spacing for more breathing room
                float y = -topMargin - row * (itemHeight + spacing);



                rect.anchoredPosition = new Vector2(x, y);
            }
        }
    }

    private BattleCharacter GetBattleCharacter(int index)
    {
        return charactersThatAreNotEnemies[index];
    }

    public int GetSelectedSlot(out bool isActive) // gets the selected slot and if is an active slot or stock slot
    {
        // gets the party object
        GameObject party = partyScrollContent.transform.GetChild(1).GetChild(0).GetChild(selectedParty).gameObject;

        // gets all the Selected Buttons Borders
        Image[] buttons = party.GetComponentsInChildren<Image>().Where(b => b.name == "SB").ToArray();
        int index = 0;

        isActive = false;

        if (buttons.FirstOrDefault(b => b.enabled)) // if there is a selected button
        {
            // gets the index of the selected button
            index = buttons.FirstOrDefault(b => b.enabled).transform.parent.GetSiblingIndex();
        }
        // if there ara no selected slots, it just uses the first slot in the stock party

        return index;
    }
}
