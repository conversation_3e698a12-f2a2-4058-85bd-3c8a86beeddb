using UnityEngine;
using UnityEngine.UI;

public class ConfButton : MonoBehaviour
{
    public GameObject ConfMenu;

    private Button button;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        button = GetComponent<Button>();
        button.onClick.AddListener(ActivateConf);
    }

    // Update is called once per frame
    void Update()
    {

    }

    void ActivateConf()
    {
        if (ConfMenu.activeSelf) ConfMenu.SetActive(false);
        else ConfMenu.SetActive(true);
    }
}
