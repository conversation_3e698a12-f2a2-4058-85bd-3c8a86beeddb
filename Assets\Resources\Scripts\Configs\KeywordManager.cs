using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// Singleton manager for keyword localization system
/// Provides global access to localized text strings through key-based lookup
/// Loads keywords from generalInfo.json and handles graceful fallbacks for missing keys
///
/// Usage:
/// - KeywordManager.GetWord("GENERIC_ACCEPT") returns "Confirmar"
/// - KeywordManager.GetWordOrDefault("MISSING_KEY", "Default Text") returns "Default Text"
/// - KeywordManager.<PERSON><PERSON><PERSON>("GENERIC_ACCEPT") returns true/false
///
/// The system is read-only from game code perspective - all modifications must come through import process
/// </summary>
public class KeywordManager : MonoBehaviour
{
    #region Singleton Implementation
    private static KeywordManager _instance;

    /// <summary>
    /// Singleton instance accessor
    /// Automatically creates instance if none exists
    /// </summary>
    public static KeywordManager Instance
    {
        get
        {
            if (_instance == null)
            {
                // Try to find existing instance in scene
                _instance = FindFirstObjectByType<KeywordManager>();

                if (_instance == null)
                {
                    // Create new GameObject with KeywordManager component
                    GameObject keywordManagerObject = new GameObject("KeywordManager");
                    _instance = keywordManagerObject.AddComponent<KeywordManager>();
                    DontDestroyOnLoad(keywordManagerObject);
                }
            }
            return _instance;
        }
    }
    #endregion

    #region Private Fields
    /// <summary>
    /// Dictionary for fast keyword lookup by key
    /// Key: string key (e.g., "GENERIC_ACCEPT")
    /// Value: JsonKeyword object containing all keyword data
    /// </summary>
    private Dictionary<string, JsonKeyword> keywordLookup = new Dictionary<string, JsonKeyword>();

    /// <summary>
    /// Dictionary for fast modifier lookup by id
    /// Key: string id (e.g., "MO0")
    /// Value: JsonModifier object containing all modifier data
    /// </summary>
    private Dictionary<string, JsonModifier> modifierLookup = new Dictionary<string, JsonModifier>();

    /// <summary>
    /// Flag to track if keywords and modifiers have been loaded successfully
    /// </summary>
    private bool isLoaded = false;

    /// <summary>
    /// Cached reference to loaded general info data for debugging and inspection
    /// </summary>
    private JsonGeneralInfo generalInfo;
    #endregion

    #region Unity Lifecycle
    private void Awake()
    {
        // Ensure singleton pattern
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            LoadKeywords();
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }
    #endregion

    #region Public Static Methods
    /// <summary>
    /// Gets the localized word for the specified key
    /// Returns the key itself if not found (graceful fallback)
    /// </summary>
    /// <param name="key">The keyword key to look up (e.g., "GENERIC_ACCEPT")</param>
    /// <returns>Localized word or the key itself if not found</returns>
    public static string GetWord(string key)
    {
        return Instance.GetWordInternal(key);
    }

    /// <summary>
    /// Gets the localized word for the specified key with a custom default
    /// Returns the provided default value if key is not found
    /// </summary>
    /// <param name="key">The keyword key to look up</param>
    /// <param name="defaultValue">Default value to return if key not found</param>
    /// <returns>Localized word or default value if not found</returns>
    public static string GetWordOrDefault(string key, string defaultValue)
    {
        return Instance.GetWordOrDefaultInternal(key, defaultValue);
    }

    /// <summary>
    /// Checks if a keyword key exists in the system
    /// </summary>
    /// <param name="key">The keyword key to check</param>
    /// <returns>True if key exists, false otherwise</returns>
    public static bool HasKey(string key)
    {
        return Instance.HasKeyInternal(key);
    }

    /// <summary>
    /// Gets the complete keyword object for advanced usage
    /// Returns null if key not found
    /// </summary>
    /// <param name="key">The keyword key to look up</param>
    /// <returns>JsonKeyword object or null if not found</returns>
    public static JsonKeyword GetKeyword(string key)
    {
        return Instance.GetKeywordInternal(key);
    }

    /// <summary>
    /// Gets all available keyword keys for debugging or UI population
    /// </summary>
    /// <returns>Array of all available keyword keys</returns>
    public static string[] GetAllKeys()
    {
        return Instance.GetAllKeysInternal();
    }

    /// <summary>
    /// Forces reload of keywords from generalInfo.json
    /// Useful after import operations or for debugging
    /// </summary>
    public static void ReloadKeywords()
    {
        Instance.LoadKeywords();
    }

    /// <summary>
    /// Gets the total number of loaded keywords
    /// </summary>
    /// <returns>Number of keywords currently loaded</returns>
    public static int GetKeywordCount()
    {
        return Instance.keywordLookup.Count;
    }

    /// <summary>
    /// Gets the skill name for the specified modifier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>Skill name or the id itself if not found</returns>
    public static string GetModifierSkill(string id)
    {
        return Instance.GetModifierSkillInternal(id);
    }

    /// <summary>
    /// Gets the description for the specified modifier id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>Description text or empty string if not found</returns>
    public static string GetModifierDescription(string id)
    {
        return Instance.GetModifierDescriptionInternal(id);
    }

    /// <summary>
    /// Gets the acronym for the specified modifier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>Acronym or the id itself if not found</returns>
    public static string GetModifierAcronym(string id)
    {
        return Instance.GetModifierAcronymInternal(id);
    }

    /// <summary>
    /// Gets the complete modifier object for advanced usage
    /// Returns null if id not found
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>JsonModifier object or null if not found</returns>
    public static JsonModifier GetModifier(string id)
    {
        return Instance.GetModifierInternal(id);
    }

    /// <summary>
    /// Gets all available modifier ids for debugging or UI population
    /// </summary>
    /// <returns>Array of all available modifier ids</returns>
    public static string[] GetAllModifierIds()
    {
        return Instance.GetAllModifierIdsInternal();
    }

    /// <summary>
    /// Gets the total number of loaded modifiers
    /// </summary>
    /// <returns>Number of modifiers currently loaded</returns>
    public static int GetModifierCount()
    {
        return Instance.modifierLookup.Count;
    }

    // Convenience methods for the 6 specific modifiers

    // Skill name methods
    /// <summary>Gets the skill name for Knowledge modifier (MO0)</summary>
    public static string GetKnowledgeSkillName() => GetModifierSkill("MO0");

    /// <summary>Gets the skill name for Luck modifier (MO1)</summary>
    public static string GetLuckSkillName() => GetModifierSkill("MO1");

    /// <summary>Gets the skill name for Speed modifier (MO2)</summary>
    public static string GetSpeedSkillName() => GetModifierSkill("MO2");

    /// <summary>Gets the skill name for Precision modifier (MO3)</summary>
    public static string GetPrecisionSkillName() => GetModifierSkill("MO3");

    /// <summary>Gets the skill name for Evasion modifier (MO4)</summary>
    public static string GetEvasionSkillName() => GetModifierSkill("MO4");

    /// <summary>Gets the skill name for Critical Chance modifier (MO5)</summary>
    public static string GetCriticalChanceSkillName() => GetModifierSkill("MO5");

    // Acronym methods
    /// <summary>Gets the acronym for Knowledge modifier (MO0)</summary>
    public static string GetKnowledgeAcronym() => GetModifierAcronym("MO0");

    /// <summary>Gets the acronym for Luck modifier (MO1)</summary>
    public static string GetLuckAcronym() => GetModifierAcronym("MO1");

    /// <summary>Gets the acronym for Speed modifier (MO2)</summary>
    public static string GetSpeedAcronym() => GetModifierAcronym("MO2");

    /// <summary>Gets the acronym for Precision modifier (MO3)</summary>
    public static string GetPrecisionAcronym() => GetModifierAcronym("MO3");

    /// <summary>Gets the acronym for Evasion modifier (MO4)</summary>
    public static string GetEvasionAcronym() => GetModifierAcronym("MO4");

    /// <summary>Gets the acronym for Critical Chance modifier (MO5)</summary>
    public static string GetCriticalChanceAcronym() => GetModifierAcronym("MO5");

    // Description methods
    /// <summary>Gets the description for Knowledge modifier (MO0)</summary>
    public static string GetKnowledgeDescription() => GetModifierDescription("MO0");

    /// <summary>Gets the description for Luck modifier (MO1)</summary>
    public static string GetLuckDescription() => GetModifierDescription("MO1");

    /// <summary>Gets the description for Speed modifier (MO2)</summary>
    public static string GetSpeedDescription() => GetModifierDescription("MO2");

    /// <summary>Gets the description for Precision modifier (MO3)</summary>
    public static string GetPrecisionDescription() => GetModifierDescription("MO3");

    /// <summary>Gets the description for Evasion modifier (MO4)</summary>
    public static string GetEvasionDescription() => GetModifierDescription("MO4");

    /// <summary>Gets the description for Critical Chance modifier (MO5)</summary>
    public static string GetCriticalChanceDescription() => GetModifierDescription("MO5");
    #endregion

    #region Private Implementation Methods
    /// <summary>
    /// Internal method to load keywords and modifiers from generalInfo.json
    /// Called automatically on initialization and can be called manually to reload
    /// </summary>
    private void LoadKeywords()
    {
        try
        {
            // Clear existing data
            keywordLookup.Clear();
            modifierLookup.Clear();
            isLoaded = false;

            // Load from JSON using existing JsonSaveHelper pattern
            if (JsonSaveHelper.FileExists("generalInfo.json"))
            {
                generalInfo = JsonSaveHelper.LoadFromJson<JsonGeneralInfo>("generalInfo.json");

                if (generalInfo != null)
                {
                    int keywordCount = 0;
                    int modifierCount = 0;

                    // Build keyword lookup dictionary
                    if (generalInfo.keywordPkg != null)
                    {
                        foreach (var keyword in generalInfo.keywordPkg)
                        {
                            if (keyword != null && !string.IsNullOrEmpty(keyword.key))
                            {
                                // Use key as dictionary key for fast lookup
                                keywordLookup[keyword.key] = keyword;
                                keywordCount++;
                            }
                        }
                    }

                    // Build modifier lookup dictionary
                    if (generalInfo.modifierListPkg != null)
                    {
                        foreach (var modifier in generalInfo.modifierListPkg)
                        {
                            if (modifier != null && !string.IsNullOrEmpty(modifier.id))
                            {
                                // Use id as dictionary key for fast lookup
                                modifierLookup[modifier.id] = modifier;
                                modifierCount++;
                            }
                        }
                    }

                    isLoaded = true;
                    //Debug.Log($"[KeywordManager] ✅ Loaded {keywordCount} keywords and {modifierCount} modifiers from generalInfo.json");
                }
                else
                {
                    Debug.LogWarning("[KeywordManager] ⚠️ generalInfo.json exists but contains no data");
                }
            }
            else
            {
                Debug.LogWarning("[KeywordManager] ⚠️ generalInfo.json not found - keywords and modifiers not available");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"[KeywordManager] ❌ Failed to load keywords and modifiers: {ex.Message}");
            isLoaded = false;
        }
    }

    private string GetWordInternal(string key)
    {
        if (string.IsNullOrEmpty(key))
            return "";

        if (keywordLookup.TryGetValue(key, out JsonKeyword keyword))
        {
            return keyword.word ?? key;
        }

        // Graceful fallback - return the key itself
        Debug.LogWarning($"[KeywordManager] ⚠️ Keyword key '{key}' not found, returning key as fallback");
        return key;
    }

    private string GetWordOrDefaultInternal(string key, string defaultValue)
    {
        if (string.IsNullOrEmpty(key))
            return defaultValue ?? "";

        if (keywordLookup.TryGetValue(key, out JsonKeyword keyword))
        {
            return keyword.word ?? defaultValue;
        }

        return defaultValue ?? "";
    }

    private bool HasKeyInternal(string key)
    {
        return !string.IsNullOrEmpty(key) && keywordLookup.ContainsKey(key);
    }

    private JsonKeyword GetKeywordInternal(string key)
    {
        if (string.IsNullOrEmpty(key))
            return null;

        keywordLookup.TryGetValue(key, out JsonKeyword keyword);
        return keyword;
    }

    private string[] GetAllKeysInternal()
    {
        return keywordLookup.Keys.ToArray();
    }

    private string GetModifierSkillInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (modifierLookup.TryGetValue(id, out JsonModifier modifier))
        {
            return modifier.skill ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[KeywordManager] ⚠️ Modifier id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetModifierDescriptionInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (modifierLookup.TryGetValue(id, out JsonModifier modifier))
        {
            return modifier.description ?? "";
        }

        return "";
    }

    private string GetModifierAcronymInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (modifierLookup.TryGetValue(id, out JsonModifier modifier))
        {
            return modifier.acronym ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[KeywordManager] ⚠️ Modifier id '{id}' not found, returning id as fallback");
        return id;
    }

    private JsonModifier GetModifierInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        modifierLookup.TryGetValue(id, out JsonModifier modifier);
        return modifier;
    }

    private string[] GetAllModifierIdsInternal()
    {
        return modifierLookup.Keys.ToArray();
    }
    #endregion

    #region Debug and Utility Methods
    /// <summary>
    /// Debug method to print all loaded keywords to console
    /// Useful for development and troubleshooting
    /// </summary>
    [ContextMenu("Debug Print All Keywords")]
    public void DebugPrintAllKeywords()
    {
        if (!isLoaded)
        {
            Debug.Log("[KeywordManager] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[KeywordManager] 🔍 Loaded Keywords ({keywordLookup.Count} total):");
        foreach (var kvp in keywordLookup)
        {
            var keyword = kvp.Value;
            string tags = keyword.keywordTags != null ? string.Join(", ", keyword.keywordTags) : "none";
            Debug.Log($"  Key: '{keyword.key}' | Word: '{keyword.word}' | ID: '{keyword.id}' | Tags: [{tags}]");
        }
    }

    /// <summary>
    /// Debug method to print all loaded modifiers to console
    /// Useful for development and troubleshooting
    /// </summary>
    [ContextMenu("Debug Print All Modifiers")]
    public void DebugPrintAllModifiers()
    {
        if (!isLoaded)
        {
            Debug.Log("[KeywordManager] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[KeywordManager] 🔍 Loaded Modifiers ({modifierLookup.Count} total):");
        foreach (var kvp in modifierLookup)
        {
            var modifier = kvp.Value;
            Debug.Log($"  ID: '{modifier.id}' | Skill: '{modifier.skill}' | Acronym: '{modifier.acronym}' | Description: '{modifier.description}'");
        }
    }

    /// <summary>
    /// Debug method to print all loaded data (keywords and modifiers)
    /// </summary>
    [ContextMenu("Debug Print All Data")]
    public void DebugPrintAllData()
    {
        DebugPrintAllKeywords();
        DebugPrintAllModifiers();
    }

    /// <summary>
    /// Gets loading status for debugging
    /// </summary>
    /// <returns>True if keywords are loaded successfully</returns>
    public bool IsLoaded()
    {
        return isLoaded;
    }
    #endregion
}
