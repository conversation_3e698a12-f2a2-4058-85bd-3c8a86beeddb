[{"directory": "D:/<PERSON><PERSON>/NEW-DKG-git-mobile/.utmp/RelWithDebInfo/4i1a5u3k/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.36F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android29 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/Menino <PERSON>tista/NEW-DKG-git-mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAApplication.cpp.o -c \"D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAApplication.cpp\"", "file": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAApplication.cpp"}, {"directory": "D:/<PERSON><PERSON>/NEW-DKG-git-mobile/.utmp/RelWithDebInfo/4i1a5u3k/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.36F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android29 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/Menino <PERSON>tista/NEW-DKG-git-mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAConfiguration.cpp.o -c \"D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAConfiguration.cpp\"", "file": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAConfiguration.cpp"}, {"directory": "D:/<PERSON><PERSON>/NEW-DKG-git-mobile/.utmp/RelWithDebInfo/4i1a5u3k/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.36F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android29 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/Menino <PERSON>tista/NEW-DKG-git-mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGADebug.cpp.o -c \"D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGADebug.cpp\"", "file": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGADebug.cpp"}, {"directory": "D:/<PERSON><PERSON>/NEW-DKG-git-mobile/.utmp/RelWithDebInfo/4i1a5u3k/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.36F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android29 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/Menino <PERSON>tista/NEW-DKG-git-mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAEntry.cpp.o -c \"D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAEntry.cpp\"", "file": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAEntry.cpp"}, {"directory": "D:/<PERSON><PERSON>/NEW-DKG-git-mobile/.utmp/RelWithDebInfo/4i1a5u3k/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.36F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android29 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/Menino <PERSON>tista/NEW-DKG-git-mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAInput.cpp.o -c \"D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInput.cpp\"", "file": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInput.cpp"}, {"directory": "D:/<PERSON><PERSON>/NEW-DKG-git-mobile/.utmp/RelWithDebInfo/4i1a5u3k/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.36F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android29 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/Menino <PERSON>tista/NEW-DKG-git-mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAInputKeyEvent.cpp.o -c \"D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputKeyEvent.cpp\"", "file": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputKeyEvent.cpp"}, {"directory": "D:/<PERSON><PERSON>/NEW-DKG-git-mobile/.utmp/RelWithDebInfo/4i1a5u3k/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.36F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android29 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/Menino <PERSON>tista/NEW-DKG-git-mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGAInputMotionEvent.cpp.o -c \"D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputMotionEvent.cpp\"", "file": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGAInputMotionEvent.cpp"}, {"directory": "D:/<PERSON><PERSON>/NEW-DKG-git-mobile/.utmp/RelWithDebInfo/4i1a5u3k/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.36F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android29 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_GAME_ACTIVITY_CODE -Dgame_EXPORTS -I\"D:/Menino <PERSON>tista/NEW-DKG-git-mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/4533d298259fc52a43021fce53f5e4a9/transformed/jetified-games-activity-3.0.5/prefab/modules/game-activity_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o GameActivity\\CMakeFiles\\game.dir\\UGASoftKeyboard.cpp.o -c \"D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGASoftKeyboard.cpp\"", "file": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\UGASoftKeyboard.cpp"}, {"directory": "D:/<PERSON><PERSON>/NEW-DKG-git-mobile/.utmp/RelWithDebInfo/4i1a5u3k/arm64-v8a", "command": "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.36F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android29 --sysroot=\"C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DEXTERNAL_FRAME_PACING_CODE -Dswappywrapper_EXPORTS -I\"D:/Menino <PERSON>tista/NEW-DKG-git-mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing\" -isystem \"C:/Users/<USER>/.gradle/caches/transforms-3/268849a49ea9eb2bba6f4e0ac95bfd63/transformed/jetified-games-frame-pacing-1.10.0/prefab/modules/swappy_static/include\" -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC -o FramePacing\\CMakeFiles\\swappywrapper.dir\\UnitySwappyWrapper.cpp.o -c \"D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\FramePacing\\UnitySwappyWrapper.cpp\"", "file": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\FramePacing\\UnitySwappyWrapper.cpp"}]