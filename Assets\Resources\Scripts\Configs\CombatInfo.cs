using UnityEngine;
using UnityEngine.UI;

public class CombatInfo : MonoBehaviour
{

    public GameObject PlayerTurn, EnemyTurn, Turn, Attacks, Combo, FPSCounter;

    public Camera mainCam;
    public Camera configCam;
    public Canvas GamePlayCanvas;
    public Canvas ConfigsCanvas;


    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        GetComponent<Button>().onClick.AddListener(Hide);
    }

    private void Hide()
    {
        if (PlayerTurn.activeSelf) PlayerTurn.SetActive(false); else PlayerTurn.SetActive(true);
        if (EnemyTurn.activeSelf) EnemyTurn.SetActive(false); else EnemyTurn.SetActive(true);
        if (Turn.activeSelf) Turn.SetActive(false); else Turn.SetActive(true);
        if (Attacks.activeSelf) Attacks.SetActive(false); else Attacks.SetActive(true);
        if (Combo.activeSelf) Combo.SetActive(false); else Combo.SetActive(true);
        if (FPSCounter.activeSelf) FPSCounter.SetActive(false); else FPSCounter.SetActive(true);
    }
}
