{"buildFiles": ["D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\FramePacing\\CMakeLists.txt", "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Menino Autista\\NEW-DKG-git-mobile\\.utmp\\RelWithDebInfo\\4i1a5u3k\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Menino Autista\\NEW-DKG-git-mobile\\.utmp\\RelWithDebInfo\\4i1a5u3k\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"game::@d02bb112ea9f9c2ed29f": {"artifactName": "game", "abi": "arm64-v8a", "output": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\RelWithDebInfo\\4i1a5u3k\\obj\\arm64-v8a\\libgame.so", "runtimeFiles": []}, "swappywrapper::@7e25bd1f32ce224db4e9": {"artifactName": "swappywrapper", "abi": "arm64-v8a", "output": "D:\\Menino Autista\\NEW-DKG-git-mobile\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\RelWithDebInfo\\4i1a5u3k\\obj\\arm64-v8a\\libswappywrapper.so", "runtimeFiles": []}}}