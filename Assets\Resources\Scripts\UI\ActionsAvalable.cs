using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ActionsAvalable : MonoBehaviour
{
    // Reference to the ConfigsHandler
    ConfigsHandler configsHandler;

    Sprite playerEmptyAction, playerUsableAction, usedAction;

    public TextMeshProUGUI actionsAvalable;


    float scaleUp = 1f, scaleDown = 0f;

    bool isPlayerTurn;
    void Start()
    {
        // Get the ConfigsHandler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();



        playerEmptyAction = Resources.Load<Sprite>("Sprites/UI/PlayerEmptyAction");
        playerUsableAction = Resources.Load<Sprite>("Sprites/UI/PlayerAction");

        usedAction = Resources.Load<Sprite>("Sprites/blank");
    }


    void Update()
    {
        // Check if it's the player's turn - if playerTurns and enemyTurns values is equal is always the Player turn 
        isPlayerTurn = configsHandler.playerTurn;

        if (isPlayerTurn) 
        {
            // Get the numOfActions from the PLayer or Enemy from ConfigHandler
            int numOfActions = configsHandler.playerActions;


            // Get the numOfEmptyActions from the PLayer or Enemy from ConfigHandler
            int numOfEmptyActions = configsHandler.playerEmptyActions;


            // Update the text with the current actions available 
            actionsAvalable.text = numOfActions.ToString();


            for (int i = 0; i < 4; i++)
            {
                Image actionImage = transform.GetChild(i).GetComponent<Image>();

                if (i >= numOfEmptyActions && i < numOfActions + numOfEmptyActions)
                {
                    actionImage.transform.localScale = Vector3.one * Mathf.MoveTowards(actionImage.transform.localScale.x, scaleUp, Time.deltaTime * 2);

                    if (Mathf.Abs(actionImage.transform.localScale.x - scaleUp) < 0.0001f) scaleUp = 1f;
                    if (Mathf.Abs(actionImage.transform.localScale.x - 1.5f) < 0.0001f) scaleUp = 1.5f;
                    if (isPlayerTurn) actionImage.sprite = playerUsableAction;
                }
                else if (i < numOfEmptyActions && actionImage.transform.localScale.x > 0)
                {
                    scaleDown = i == configsHandler.lostActions && ((Mathf.Abs(scaleDown) < 0.0001f && Mathf.Abs(actionImage.transform.localScale.x - 1f) < 0.0001f) || (actionImage.transform.localScale.x >= 1 && Mathf.Abs(scaleDown) > 0.0001f && Mathf.Abs(actionImage.transform.localScale.x - scaleDown) > 0.0001f)) ? 1.5f : 0f;

                    actionImage.transform.localScale = Vector3.MoveTowards(actionImage.transform.localScale, Vector3.one * scaleDown, Time.deltaTime * 4);
                }
                else if (actionImage.sprite != playerEmptyAction && i >= numOfEmptyActions)
                {
                    if (isPlayerTurn) actionImage.sprite = playerEmptyAction;
                    if (actionImage.transform.localScale.x < 1) actionImage.transform.localScale = Vector3.one;
                }
            }

        }
        else
        {
            // Reset the UI to its initial state
            actionsAvalable.text = "";
            for (int i = 0; i < 4; i++)
            {
                Image actionImage = transform.GetChild(i).GetComponent<Image>();
                actionImage.sprite = playerEmptyAction;
                actionImage.transform.localScale = Vector3.one;
            }
        }
    }

}
