{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}, {"build": "GameActivity", "jsonFile": "directory-GameActivity-RelWithDebInfo-3edf2ef9e25ca0503118.json", "minimumCMakeVersion": {"string": "3.4.1"}, "parentIndex": 0, "projectIndex": 1, "source": "GameActivity", "targetIndexes": [0]}, {"build": "FramePacing", "jsonFile": "directory-FramePacing-RelWithDebInfo-7f9c8865fd027a154c90.json", "minimumCMakeVersion": {"string": "3.4.1"}, "parentIndex": 0, "projectIndex": 2, "source": "FramePacing", "targetIndexes": [1]}], "name": "RelWithDebInfo", "projects": [{"childIndexes": [1, 2], "directoryIndexes": [0], "name": "Unity"}, {"directoryIndexes": [1], "name": "game", "parentIndex": 0, "targetIndexes": [0]}, {"directoryIndexes": [2], "name": "swappywrapper", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 1, "id": "game::@d02bb112ea9f9c2ed29f", "jsonFile": "target-game-RelWithDebInfo-905acbd34b7db1ca2339.json", "name": "game", "projectIndex": 1}, {"directoryIndex": 2, "id": "swappywrapper::@7e25bd1f32ce224db4e9", "jsonFile": "target-swappywrapper-RelWithDebInfo-26fef09a19fa71143fb3.json", "name": "swappywrapper", "projectIndex": 2}]}], "kind": "codemodel", "paths": {"build": "D:/Menino <PERSON>/NEW-DKG-RPG_mobile/.utmp/RelWithDebInfo/6n1c5y32/arm64-v8a", "source": "D:/<PERSON><PERSON>/NEW-DKG-RPG_mobile/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp"}, "version": {"major": 2, "minor": 3}}