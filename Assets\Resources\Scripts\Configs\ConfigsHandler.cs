using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using TMPro;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.UI;


public class ConfigsHandler : MonoBehaviour
{
    public Canvas canvas;
    public Camera mainCamera;
    public int[] ComboChance = new int[7], GoldenStrike = new int[7]; // combo chance array

    public int currentCombo = -1, playerTurns = 0, enemyTurns = 0, turns = 0, playerActions = 1, enemyActions = 1, playerEmptyActions = 0, enemyEmptyActions = 0, lostActions = -1, money = 0, ModFraction = 10; // turn counters

    public int pIndex = 0; // attacked player index
    public int attackedPlayer = 0;

    public int energyTimer = 7; // max energy
    public float energyLeft = 7, ppLeft = 100f, shakeStrength = 5.3f; // current energy/power left

    public bool canCountdown = false; // the bool that controls the energy countdown

    // the default values of B, C, D, E, F, G, Difficulty, StockWeight, MaxPP, ReductionPerPiece and FractionOfAttacksPerAction
    [HideInInspector] public float B = 25, C = 2.5f, D = 0.5f, E = 0.04f, F = 1f, G = 1.5f, Difficulty = 1, StockWeight = 0.5f, MaxPP = 100f, ReductionPerCombo = 1f, FractionOfAttacksPerAction = 2f, IDBOffset = 0f;

    // the gameobjects that will be used to display the values, chance configurations and gameplay UI
    [HideInInspector]
    public GameObject
        StatsValues, SkillsValues,
        ModsValues, ActionMenu,
        SkillSelection, ChangeWithStock,
        CharactersForParty, EscapePopup,
        EscapeFailPopup, WinPopup,
        FailPopup, LosePopup,
        infiniteToggle, ChangeOnlyNulls,
        ShowHP, Coins,
        GamePlayConfs;

    public AudioSource MusicPlayer;

    public int partySelected = -1;

    GridManager grid; // the grid creator

    private bool[] playerSelected = { true, false, false, false }; // a bool array to control the player selection
    private bool[] enemySelected = { true, false, false, false }; // a bool array to control the enemy selection

    int selectedEnemy;

    List<BattleCharacter> characters = new(), enemies = new(); // a list to store the characters
    public List<PartyCharacters> partyCharacters = new(); // a list to store the party characters

    public List<GameObject> playersInterface = new(); // a list to store the players interfaces
    public List<GameObject> enemiesInterface = new();

    public List<CharacterSkills> defaultSkills = new(); // list of default skills
    public List<CharacterStatus> defaultStats = new(); // list of default stats
    public List<CharacterMods> defaultMods = new(); // list of default mods

    public BattleCharacter[] pC = { null, null, null, null }; // an array to store the player characters positions
    public BattleCharacter[] eC = { null, null, null, null }; // an array to store the enemy characters positions

    public LoadValues LoadedValues; // the gameobject that handles loading and saving of values

    public bool canCheckWhoStarts = true, stopGetAllEnemies = false;

    public bool playerTurn = true;

    public long damageNumber;

    public bool physicalAttack;
    public bool absorbedAttack;
    public bool repelledAttack;
    public bool blockedAttack;
    public bool parriedAttack;
    public bool counterLabel;

    public bool isCounter;
    public int parryCount;

    public bool enemyFirstStrike = false;

    public bool win = false;

    public Dictionary<Types, int> enemyAttacksType = new(); // a dictionary to store the attacks type and the amount of times it was used by enemies
    public Dictionary<Types, int> playerAttacksType = new(); // a dictionary to store the attacks type and the amount of times it was used by players


    private void Awake()
    {

        for (int i = 0; i < pC.Length; i++) playersInterface.Add(GameObject.Find("Player" + i)); // gets the player interfaces
        for (int i = 0; i < eC.Length; i++) enemiesInterface.Add(GameObject.Find("Enemy" + i)); // gets the enemy interfaces

        try
        {
            grid = GameObject.Find("GridManager").GetComponent<GridManager>(); // gets the GridManager component
        }
        catch (Exception ex)
        {
            Debug.LogError("An error occurred: " + ex.Message);
        }
        // gets the gameobjects that will be used
        StatsValues = GameObject.Find("StatsValues");
        SkillsValues = GameObject.Find("SkillsValues");
        ModsValues = GameObject.Find("ModsValues");
        ActionMenu = GameObject.Find("ActionMenu");
        SkillSelection = GameObject.Find("SkillSelection");
        ChangeWithStock = GameObject.Find("ChangeWithStock");
        CharactersForParty = GameObject.Find("PartyConfs");
        EscapePopup = GameObject.Find("EscapePopup");
        EscapeFailPopup = GameObject.Find("EscapeFailPopup");
        WinPopup = GameObject.Find("WinPopup");
        FailPopup = GameObject.Find("FailPopup");
        LosePopup = GameObject.Find("LosePopup");
        infiniteToggle = GameObject.Find("InfiniteToggle");
        ChangeOnlyNulls = GameObject.Find("ChangeOnlyNulls");
        ShowHP = GameObject.Find("ShowHP");
        Coins = GameObject.Find("Coins");
        GamePlayConfs = GameObject.Find("GamePlayConfs");

        // sets the gameobjects to inactive
        StatsValues.SetActive(false);
        ActionMenu.SetActive(false);
        SkillSelection.SetActive(false);
        ChangeWithStock.SetActive(false);
        EscapePopup.SetActive(false);
        EscapeFailPopup.SetActive(false);
        WinPopup.SetActive(false);
        FailPopup.SetActive(false);
        LosePopup.SetActive(false);
        GamePlayConfs.SetActive(false);

        // gets the gameobject that handles loading and saving of values
        LoadedValues = GameObject.Find("LoadedValues").GetComponent<LoadValues>();

        // loads the values
        energyTimer = LoadedValues.energyTimer;
        energyLeft = energyTimer;

        ModFraction = LoadedValues.ModFraction;

        ComboChance = LoadedValues.ComboChance;

        GoldenStrike = LoadedValues.GoldenStrike;

        B = LoadedValues.B;
        C = LoadedValues.C;
        D = LoadedValues.D;
        E = LoadedValues.E;
        F = LoadedValues.F;
        G = LoadedValues.G;
        Difficulty = LoadedValues.Difficulty;
        StockWeight = LoadedValues.StockWeight;
        MaxPP = LoadedValues.MaxPP;
        ReductionPerCombo = LoadedValues.ReductionPerCombo;
        FractionOfAttacksPerAction = LoadedValues.FractionOfAttacksPerAction;
        IDBOffset = LoadedValues.IDBOffset;

        defaultMods = LoadedValues.defaultMods;
        defaultSkills = LoadedValues.defaultSkills;
        defaultStats = LoadedValues.defaultStats;

        partyCharacters = LoadedValues.partyCharacters;

        // Ensure we always have exactly 5 parties (safety check for JSON migration)
        while (partyCharacters.Count < 5)
        {
            partyCharacters.Add(new PartyCharacters($"Party{partyCharacters.Count}"));
        }

        characters = LoadedValues.characters;

        // Volume control is now handled entirely by ConfMenu

        if (LoadedValues.didLoad) StartCoroutine(LoadCharacterUI());

        GameObject.Find("AIConfs").SetActive(false); // sets the buffs UI to inactive
        GameObject.Find("PartyConfs").SetActive(false); // sets the party UI to inactive
        GameObject.Find("ResurrectConfs").SetActive(false); // sets the party UI to inactive

        infiniteToggle.GetComponent<Toggle>().isOn = LoadedValues.infinity; // loads the infinity toggle, only works on reset

        infiniteToggle.GetComponent<Toggle>().onValueChanged.AddListener(b => SaveValues()); // when the toggle is changed, saves the infinity toggle value

        ChangeOnlyNulls.GetComponent<Toggle>().isOn = LoadedValues.changeOnlyNulls; // loads the ChangeOnlyNulls toggle, only works on reset

        ChangeOnlyNulls.GetComponent<Toggle>().onValueChanged.AddListener(b => SaveValues()); // when the toggle is changed, saves the ChangeOnlyNulls toggle value

        ShowHP.GetComponent<Toggle>().isOn = LoadedValues.showHP;

        ShowHP.GetComponent<Toggle>().onValueChanged.AddListener(b => SaveValues());

        canCountdown = false;

        ppLeft = MaxPP; // sets the power left to the max power

        SetEnergyTime();

        enemyActions = 1;

        // Initializes the attacks type dictionary with all types and sets their count to 0
        enemyAttacksType = new Dictionary<Types, int>
        {
            { Types.Strength, 0 },
            { Types.Magic, 0 },
            { Types.Fire, 0 },
            { Types.Venom, 0 },
            { Types.Possession, 0 },
            { Types.Electricity, 0 },
            { Types.Acid, 0 },
            { Types.Frost, 0 }
        };

        playerAttacksType = new Dictionary<Types, int>
        {
            { Types.Strength, 0 },
            { Types.Magic, 0 },
            { Types.Fire, 0 },
            { Types.Venom, 0 },
            { Types.Possession, 0 },
            { Types.Electricity, 0 },
            { Types.Acid, 0 },
            { Types.Frost, 0 }
        };
    }

    private void Update()
    {
        // Volume control is now handled entirely by ConfMenu

        Coins.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = money.ToString();

        for (int i = 0; i < pC.Length; i++) if (pC[i] != null && (pC[i].isEnemy || GetCharacter(pC[i]) == -1)) pC[i] = null; // removes the player if they stopped existing in the characters list or if they are an enemy

        for (int i = 0; i < eC.Length; i++) if (eC[i] != null && (!eC[i].isEnemy || GetCharacter(eC[i]) == -1)) eC[i] = null; // same thing to the loop above but for the enemies

        foreach (var enemy in eC) if (enemy != null && partySelected != -1) // sets the max hp according to the sum of the active characters hp plus the stock weight times the sum of the stock characters hp
            {

                float fLevel = Mathf.Min(1, Mathf.Max(enemy.level, 0.5f) / Mathf.Max(CalculatePartyAverageLevel(), 1));

                float sumOfActives = partyCharacters[partySelected].activeCharacters.Where(ac => ac != null).Sum(ac => ac.stats.GetHp(ac.level));
                float sumOfStock = partyCharacters[partySelected].stockCharacters.Where(sc => sc != null && partyCharacters[partySelected].activeCharacters.FirstOrDefault(ac => ac == sc) == null).Sum(sc => sc.stats.GetHp(sc.level));

                // max ( baseHP, ((ActiveSum + stockWeight * StockSum) / NumBosses) * fLevel * Difficulty)
                float calculatedHP = Mathf.Max(enemy.stats.GetHp(enemy.level),
                    (sumOfActives + StockWeight * sumOfStock) / eC.Count(ec => ec != null) * fLevel * Difficulty);

                if (enemy.maxHP != (long)calculatedHP && (!enemy.hpAddapted || (playerTurns == 0 && playerActions == 1 && playerEmptyActions == 0)) && !enemy.IsDead) // checks if it should update the hp for the enemy
                {
                    enemy.hpAddapted = true;
                    enemy.maxHP = (long)calculatedHP;
                    enemy.Heal(enemy.maxHP);
                }
            }

        if (GetNumberOfEnemies() == 1)
        {
            enemySelected = new bool[] { true, false, false, false };
        }

        //resets the player's hp, to prevent the player to have the hp of an enemy
        foreach (var player in pC) if (player != null && player.maxHP != player.stats.GetHp()[player.level]) { player.SetLevel(player.level); player.hpAddapted = false; }

        var notNullEnemies = eC.Where(e => e != null && !e.IsDead).ToArray();
        var nullEnemies = eC.Where(e => e == null || (e != null && e.IsDead)).ToArray();

        if (GetNumberOfEnemies() != 4) eC = notNullEnemies.Concat(nullEnemies).ToArray();

        partyCharacters.ForEach(party =>
        {
            party.activeCharacters = party.activeCharacters.Select(c => party.stockCharacters.FirstOrDefault(sC => sC == c) == null ? null : c).ToArray();
            party.stockCharacters = party.stockCharacters.Select(c => c != null && c.isEnemy ? null : c).ToArray();

            var hasSeen = new HashSet<BattleCharacter>();

            party.stockCharacters = party.stockCharacters.Select(c => c == null || !hasSeen.Add(c) ? null : c).ToArray();

        });

        if (pC[Array.IndexOf(playerSelected, true)] != null &&
            (pC[Array.IndexOf(playerSelected, true)].IsDead || pC[Array.IndexOf(playerSelected, true)].isStunned)
            && pC.Any(pc => pc == null || (pc != null && !pc.IsDead && !pc.isStunned && playersInterface[Array.IndexOf(pC, pc)].GetComponent<PlayerInterface>().AttackCooldown.fillAmount <= 0.001f)))
        {
            BattleCharacter character = pC.FirstOrDefault(pc => pc == null || (pc != null && !pc.IsDead && !pc.isStunned && playersInterface[Array.IndexOf(pC, pc)].GetComponent<PlayerInterface>().AttackCooldown.fillAmount <= 0.001f));
            int index = character != null || Array.IndexOf(pC, character) != -1 ? Array.IndexOf(pC, character) : 3;
            SetPlayerSelected(index);
        }

        if (IsThereCharacters() && canCheckWhoStarts && Camera.main != null)
        {
            float AGIparty = 0;
            float AGIenemies = 0;

            foreach (var ec in eC)
            {
                if (ec != null) AGIenemies += ec.mods.GetSpeed();
            }

            foreach (var pc in pC)
            {
                if (pc != null) AGIparty += pc.mods.GetSpeed();
            }

            AGIparty /= partyCharacters[partySelected].activeCharacters.Count(pc => pc != null);
            AGIenemies /= eC.Count(pc => pc != null);
            if (AGIparty < AGIenemies || (50 < UnityEngine.Random.Range(0, 100) && Mathf.Abs(AGIparty - AGIenemies) < 0.0001f))
            {
                playersInterface[GetSelectedPlayer()].GetComponent<PlayerInterface>().didCrit = true;
                playerTurn = false;
                enemyFirstStrike = true;
                GameObject.Find("FirstAdvantage").GetComponent<FirstAdvantageAnim>().PlayCenterAnimation();
                StartCoroutine(grid.SwitchTurns());
            }
            else
            {
                GameObject.Find("FirstAdvantage").GetComponent<FirstAdvantageAnim>().PlayCenterAnimation();
            }
            canCheckWhoStarts = false;
        }

        // if there are more enemies in the characters list than in the enemies array, it fills it up, it only does that if the enemy array is not full,
        // dead enemies are counted as not null, if you want to add new enemies to replace the dead ones, just call SetEnemyCharacter with false
        // default state will only replace ones that are null
        if ((Array.IndexOf(eC, null) != -1 || eC.FirstOrDefault(ec => ec.IsDead) != null) && enemies.Count >= eC.Length)
        {
            BattleCharacter enemy = enemies.Find(ec => ec != null && !ec.IsDead && Array.IndexOf(eC, ec) == -1); // gets the first enemy that isn't dead and isn't being used in the enemies array

            if (enemy != null) StartCoroutine(SetEnemyCharacter(enemy, ChangeOnlyNulls.GetComponent<Toggle>().isOn)); // if found switches the character
        }

        // Shows Win popup if all enemies are dead
        if (AreAllEnemiesDead() && win) WinPopup.SetActive(true); else WinPopup.SetActive(false);

        // Shows Fail popup if all players are dead
        //if (AreAllActivePlayersDead()) FailPopup.SetActive(true); else FailPopup.SetActive(false);

        LosePopup.SetActive(!IsPPLeft());

    }

    IEnumerator LoadCharacterUI()
    {
        GameObject loadingCameraOBJ = GameObject.Find("LoadingCamera");
        Camera loadingCamera = loadingCameraOBJ.GetComponent<Camera>();

        loadingCamera.enabled = true;

        Image loadingBar = GameObject.Find("LoadingBar").GetComponent<Image>();

        // Assume you have a CharPool component in the scene
        CharPool charPool = FindFirstObjectByType<CharPool>();

        // Only create enough UI objects for visible area + buffer
        int poolSize = charPool.poolSize;

        int totalCharacters = characters.Count;

        // Show only the first N characters (visible area)

        // Process enemies first (regardless of poolSize) / Without this if there is a char marked as enemy outside of poolSize the game will not process it
        for (int i = 0; i < totalCharacters; i++)
        {
            var character = characters[i];

            if (character.isEnemy)
            {
                character.ResetBuffs();
                yield return StartCoroutine(SetEnemyCharacter(character));
                character.SetLevel(character.level);
                character.configsHandler = this;
                character.hpAddapted = false;
            }

            if (i % 20 == 0) yield return new WaitForSeconds(0);
        }

        //Create UI for only the visible characters ---
        for (int i = 0; i < Mathf.Min(poolSize, totalCharacters); i++)
        {
            var character = characters[i];

            if (!character.isEnemy) // Already processed above
            {
                character.ResetBuffs();
                character.SetLevel(character.level);
                character.configsHandler = this;
                character.hpAddapted = false;
            }

            loadingBar.fillAmount = i / (float)totalCharacters;
            LoadedValues.loadingInfoText = $"Loading Character UI ({i + 1} of {totalCharacters})";

            if (i % 10 == 0) yield return new WaitForSeconds(0);
        }

        LoadedValues.loadingInfoText = "Loading Complete!";

        loadingBar.fillAmount = 1;

        yield return new WaitForSeconds(0.5f);

        loadingCamera.enabled = false;
    }


    public IEnumerator EnemyAttack(int totalamount) // function that makes the enemies attack, they attack the same amount that the player attacked them
    {
        totalamount = Mathf.Max(1, totalamount); // makes sure the amount of hits is at least 1
        int amount = totalamount; // sets the amount of hits to the total amount of hits


        do
        {
            selectedEnemy = Array.IndexOf(enemySelected, true);

            if (selectedEnemy == -1 || (selectedEnemy != -1 && enemiesInterface[selectedEnemy].GetComponent<EnemyInterface>().AttackCooldown.fillAmount > 0.0001f))
            {
                do
                {
                    if (eC.FirstOrDefault(ec => ec != null && !ec.IsDead && !ec.isStunned) == null || !IsThereAnyEnemyNotInCooldown()) break;
                    yield return null;
                    selectedEnemy = UnityEngine.Random.Range(0, eC.Length);
                } while (eC[selectedEnemy] == null || eC[selectedEnemy].IsDead || eC[selectedEnemy].isStunned || enemiesInterface[selectedEnemy].GetComponent<EnemyInterface>().AttackCooldown.fillAmount > 0.0001f);
            }

            yield return new WaitForSeconds(1f);

            int hits = 0;

            for (int i = 0; i < amount; i++) // loops the amount of hits
            {
                if (Array.FindIndex(pC, pc => pc != null && !pc.IsDead) == -1) break; // if there are no players alive or usable, it breaks
                if (Array.FindIndex(eC, ec => ec != null && !ec.IsDead && !ec.isStunned) == -1) break; // same thing but for the enemies
                if (!IsThereAnyEnemyNotInCooldown() || selectedEnemy == -1) break;

                pIndex = 0; // index of the player that will attack
                do
                {
                    yield return new WaitForSeconds(0);
                    pIndex = UnityEngine.Random.Range(0, 4); // picks a random player
                    attackedPlayer = pIndex; //Lets dmgLabel.cs know who will be attacked
                                             // makes sure the player is alive, is usable and is not in cooldown, if all players are in cooldown, it ignores the cooldown case
                } while (pC[pIndex] == null || pC[pIndex].IsDead || (playersInterface[pIndex].GetComponent<PlayerInterface>().AttackCooldown.fillAmount > 0.0001f && IsThereAnyPlayerNotInCooldown()));


                if (eC[selectedEnemy] == null || eC[selectedEnemy].IsDead || eC[selectedEnemy].isStunned) // switches to the next enemy that isn't dead or null
                {
                    BattleCharacter nextEnemy = eC.FirstOrDefault(ec => ec != null && !ec.IsDead && !ec.isStunned); // gets the first enemy that isn't dead or null

                    if (nextEnemy != null) // if there is a next enemy, it sets it as the selected enemy
                    {
                        selectedEnemy = Array.IndexOf(eC, nextEnemy);
                    }
                }
                var animation = enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.Data.SkeletonData.FindAnimation("Attack");

                string animationName = animation == null ? "Idle_Break" : "Attack";

                enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, animationName, false);

                yield return new WaitForSeconds(0.5f); // waits 0.5 seconds

                Types type = (Types)UnityEngine.Random.Range(0, Enum.GetValues(typeof(Types)).Length); // picks a random type

                // hits the player
                int enemySpAtk = 0;
                if (int.TryParse(eC[selectedEnemy].skills.GetValues(type).spAtk, out int spAtkValue))
                    enemySpAtk = spAtkValue;

                bool didHit = !pC[pIndex].Damage(
                    eC[selectedEnemy].stats.GetAtk(eC[selectedEnemy].level),
                    enemySpAtk,
                    type,
                    eC[selectedEnemy]
                    );
                if (didHit) hits++;

                if (pC[pIndex].stunCount > 1) // makes sure player only losses stun after 1 action
                {
                    pC[pIndex].isStunned = false;
                    pC[pIndex].stunCount = 0;
                }

                CountAttacksType(eC[selectedEnemy], type, didHit);

            }

            if (AreAllActivePlayersDead())
            {
                StartCoroutine(grid.DestroyRunes()); // If all players dead, destroy runes shows fail popup
                enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, "Idle", true);
                yield break;
            }

            if (!IsThereAnyEnemyNotInCooldown() || Array.FindIndex(eC, ec => ec != null && !ec.IsDead && !ec.isStunned) == -1) lostActions = enemyEmptyActions;

            if (parryCount > 0 && !pC[pIndex].isStunned) // If player parried enemy will suffer a counter at the the of the action
            {
                StartCoroutine(PlayerCounter(parryCount));
                enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, "Idle", true);

            for (int i = 0; i < pC.Length; i++) // Resets enemy combo count on player after enemy action
            {
                if (pC[i] != null) pC[i].combos = -1;
            }

                yield break;
            }
            enemyActions--;
            enemyEmptyActions++;

            for (int i = 0; i < pC.Length; i++) // Resets enemy combo count on player after enemy action
            {
                if (pC[i] != null) pC[i].combos = -1;
            }

            yield return null;
            try
            {

                // gets the selected enemy to check if it did crit, if it didn't crit, it applies the cooldown
                EnemyInterface enemy = enemiesInterface[selectedEnemy].GetComponent<EnemyInterface>();

                if (enemy.didCrit) enemy.didCrit = false;
                else if (enemy.AttackCooldown.fillAmount <= 0.0001f) enemy.AttackCooldown.fillAmount = 1;
                enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, "Idle", true);

            }
            catch (Exception ex)
            {
                Debug.LogError("An error occurred: " + "Index: " + selectedEnemy + " " + ex.Message);
            }

            amount = (int)Mathf.Max(1, totalamount / FractionOfAttacksPerAction); // sets the amount of hits to the total amount of hits divided by the fraction of attacks per action

        } while (enemyActions > 0); // loops until there are no more actions for the enemies

        try
        {
            enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, "Idle", true);
        }
        catch
        {
            yield break;
        }

        grid.attacks = 0; // after all the hits are finished, it resets the score

    }

    public IEnumerator EnemyCounter(int amount)
    {
        if (Array.FindIndex(pC, pc => pc != null && !pc.IsDead) == -1) yield break; // if there are no players alive or usable, it breaks

        isCounter = true;

        parryCount = 0; // resets parry counter

        for (int i = 0; i < amount; i++) // attacks the number it parried
        {
            var animation = enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.Data.SkeletonData.FindAnimation("Attack");

            string animationName = animation == null ? "Idle_Break" : "Attack";

            enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, animationName, false); // plays attack animation

            yield return new WaitForSeconds(0.5f); // waits 0.5 seconds

            Types type = (Types)UnityEngine.Random.Range(0, Enum.GetValues(typeof(Types)).Length); // picks a random type

            int enemySpAtk = 0; // makes attack physical
            int playerIndex = GetSelectedPlayer();

            pC[playerIndex].Damage(eC[selectedEnemy].stats.GetAtk(eC[selectedEnemy].level), enemySpAtk, type, eC[selectedEnemy], isCounter); // attacks player that the enemy did the parry against

            StartCoroutine(ScreenShake(10, 7)); // makes the background and the enemies shake

            if (pC[playerIndex].stunCount > 1) // makes sure player only losses stun after 1 action
            {
                pC[playerIndex].isStunned = false;
                pC[playerIndex].stunCount = 0;
            }
        }

        enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, "Idle", true); // goes back to idle animation after attacks

        if (AreAllActivePlayersDead())
        {
            StartCoroutine(grid.DestroyRunes()); // If all players dead, destroy runes shows fail popup
            enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, "Idle", true);
            yield break;
        }



        for (int i = 0; i < pC.Length; i++) // Resets enemy combo count on player after enemy action
        {
            if (pC[i] != null) pC[i].combos = -1;
        }

        yield return new WaitForSeconds(1.5f); // waits 0.5 seconds

        isCounter = false;

        // Start switch turns routine since after a counter it gains a turn
        grid.ResetActions();
        playerTurn = false;
        StartCoroutine(grid.SwitchTurns());
        yield return null;
    }

    public IEnumerator PlayerCounter(int amount)
    {
        if (Array.FindIndex(eC, ec => ec != null && !ec.IsDead) == -1) yield break; // if there are no enemies alive or usable, it breaks

        isCounter = true;

        parryCount = 0; //resets parry counter

        yield return new WaitForSeconds(0.5f); // waits 0.5 seconds

        for (int i = 0; i < amount; i++)
        {
            BattleCharacter player = pC[attackedPlayer]; // Get the player that did the parries

            yield return new WaitForSeconds(0.5f); // waits 0.5 seconds

            Types type = (Types)UnityEngine.Random.Range(0, Enum.GetValues(typeof(Types)).Length); // picks a random type

            int playerSpAtk = 0; // makes counter physical

            eC[selectedEnemy].Damage(player.stats.GetAtk(player.level), playerSpAtk, type, player, isCounter); // attacks enemy that the player did the parry against

            EnemyAnimation enemy = enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>();

            if (!enemy.gotHitted) StartCoroutine(enemy.HitAnimation(7)); // plays the hit animation

            StartCoroutine(ScreenShake(10, 7)); // makes the background and the enemies shake

        }

        isCounter = false;

        // Doesnt need to manually starts turn because player turn starts automatically after enemy does all of its attacks

        yield return null;
    }

    public void CountAttacksType(BattleCharacter character, Types type, bool didHit) // counts the amount of times a type was used by a character
    {
        if (character.isEnemy)
        {
            string charSpAtk = character.skills.GetValues(type).spAtk;

            if (!didHit) return; // if the character missed, it doesn't count

            if (string.IsNullOrEmpty(charSpAtk) || charSpAtk == "0") // if the character's spAtk is null or empty, it doesn't count
            {
                return;
            }

            if (enemyAttacksType.ContainsKey(type))
            {
                if (enemyAttacksType[type] >= 5) return; // if the type has been used 5 times, it doesn't count anymore
                enemyAttacksType[type]++;
            }
            else
            {
                enemyAttacksType[type] = 1;
            }
        }
        else
        {
            string charSpAtk = character.skills.GetValues(type).spAtk;

            if (!didHit) return; // if the character missed, it doesn't count

            if ( string.IsNullOrEmpty(charSpAtk) || charSpAtk == "0") // if the character's spAtk is null or empty, it doesn't count
            {
                return;
            }

            if (playerAttacksType.ContainsKey(type))
            {
                if (playerAttacksType[type] >= 5) return; // if the type has been used 5 times, it doesn't count anymore
                playerAttacksType[type]++;
            }
            else
            {
                playerAttacksType[type] = 1;
            }
        }
    }

    public void GuardCharacter() // sets the selected player as guarding
    {
        pC[Array.IndexOf(playerSelected, true)].isGuard = true; // sets the player as guarding, there's no need to check if the player is dead or not null because the Action Menu can't be opened if the player is dead or null

        GameObject selectedPlayer = playersInterface[Array.IndexOf(playerSelected, true)]; // gets the selected player GameObject

        Sprite icon = Resources.Load<Sprite>("Sprites/StatusEffects/Guard"); // loads the guard icon

        selectedPlayer.GetComponent<PlayerInterface>().didCrit = true;

        selectedPlayer.GetComponent<PlayerInterface>().AddStatusIcon(icon, "Guarding"); // adds the guard icon to that player interface

        playerTurn = false;
        StartCoroutine(grid.SwitchTurns()); // switches the turns
    }

    public bool IsGuarding() // returns if the selected player is guarding
    {
        if (pC[Array.IndexOf(playerSelected, true)] == null) return false; // if the player doesn't exist, it returns false
        return pC[Array.IndexOf(playerSelected, true)].statusIcons.TryGetValue("Guarding", out _); // otherwise, it checks if the player is guarding
    }

    public void RunesDamage(Types type) // damages the selected enemy with the rune type
    {

        if (!IsPPLeft()) return; // if there is no power left, it returns

        selectedEnemy = Array.IndexOf(enemySelected, true);


        if (selectedEnemy == -1)
        {
            if (eC.FirstOrDefault(ec => ec != null && !ec.IsDead) == null) return;
            do
            {
                selectedEnemy = UnityEngine.Random.Range(0, eC.Length);
            } while (eC[selectedEnemy] == null || eC[selectedEnemy].IsDead);
        }

        if (eC[selectedEnemy] == null || eC[selectedEnemy].IsDead) // switches to the next enemy if the selected one is dead or null
        {
            BattleCharacter nextEnemy = eC.FirstOrDefault(ec => ec != null && !ec.IsDead); // gets the first enemy that isn't dead or null

            if (nextEnemy != null) // if there is a next enemy, it selects it
            {
                int index = Array.IndexOf(eC, nextEnemy);

                SetEnemySelected(index);
            }
        }

        if (pC[Array.IndexOf(playerSelected, true)] == null || pC[Array.IndexOf(playerSelected, true)].IsDead || pC[Array.IndexOf(playerSelected, true)].isStunned) // same thing but for the players
        {
            BattleCharacter nextPlayer = pC.FirstOrDefault(pc => pc != null && !pc.IsDead && !pc.isStunned);

            if (nextPlayer != null)
            {
                int index = Array.IndexOf(pC, nextPlayer);

                SetPlayerSelected(index);
            }
        }

        // checks if the player and the enemy selected are alive, not null and that the player is not in cooldown
        if (eC[selectedEnemy] != null && !eC[selectedEnemy].IsDead && pC[Array.IndexOf(playerSelected, true)] != null &&
            !pC[Array.IndexOf(playerSelected, true)].IsDead && IsThereAnyPlayerNotInCooldown())
        {
            BattleCharacter atacker = pC[Array.IndexOf(playerSelected, true)]; // gets the player that is attacking

            int dmg = atacker.stats.GetAtk(atacker.level); // gets the atk of the player that is attacking at its current level

            int spAtk = 0;
            if (int.TryParse(atacker.skills.GetValues(type).spAtk, out int spAtkValue))
                spAtk = spAtkValue; // gets the spAtk of the player that is attacking for the selected type

            bool didHit = !eC[selectedEnemy].Damage(dmg, spAtk, type, atacker); // and deals the damage to the enemy

            EnemyAnimation enemy = enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>();

            if (!enemy.gotHitted) StartCoroutine(enemy.HitAnimation(7)); // plays the hit animation

            StartCoroutine(ScreenShake(shakeStrength, 7)); // makes the background and the enemies shake

            CountAttacksType(atacker, type, didHit);
        }

    }


    public bool DamageLabel(BattleCharacter reciver, long amount, Types type, bool critical = false, bool weak = false, bool superEffective = false, bool notEffective = false, bool missed = false, bool absorbed = false, bool repelled = false, bool blocked = false, bool parried = false, bool counter = false) // creates a damage label
    {
        GameObject dmgStarPrefab = Resources.Load<GameObject>("Prefabs/DamageStar"); // loads the damage star prefab
        GameObject reciverCharacter; // the character that received the damage
        GameObject attackerCharacter; // the character that did the damage
        GameObject dmgLabelPrefab;

        absorbedAttack = false;
        repelledAttack = false;
        blockedAttack = false;
        parriedAttack = false;
        counterLabel = false;
        if (parried) parryCount++; // counts the parries so the characters can counter the times it parried

        if (!reciver.isEnemy) // if the reciver is a player it gets the player GameObject
        {
            int index = Array.IndexOf(pC, reciver); // gets the index of the player that received the damage

            reciverCharacter = playersInterface[index]; // gets the player GameObject

            attackerCharacter = enemiesInterface[selectedEnemy]; // gets the enemy GameObject

            BattleCharacter attackerLabel = GetEnemyCharacter(selectedEnemy);

            switch (type)  // If any spkAtk is 0 the dmgLabel doesnt display the type icon
            {
                case Types.Strength:
                    string strSpAtkRaw = attackerLabel.skills.GetValues(Types.Strength).spAtk;
                    string strSpAtkNormalized = string.IsNullOrWhiteSpace(strSpAtkRaw) ? "0" : strSpAtkRaw;
                    if (int.TryParse(strSpAtkNormalized, out int strSpAtk) && strSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelStr"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");
                    }
                    break;
                case Types.Magic:
                    string magSpAtkRaw = attackerLabel.skills.GetValues(Types.Magic).spAtk;
                    string magSpAtkNormalized = string.IsNullOrWhiteSpace(magSpAtkRaw) ? "0" : magSpAtkRaw;
                    if (int.TryParse(magSpAtkNormalized, out int magSpAtk) && magSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelMgc"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");
                    }
                    break;
                case Types.Fire:
                    string firSpAtkRaw = attackerLabel.skills.GetValues(Types.Fire).spAtk;
                    string firSpAtkNormalized = string.IsNullOrWhiteSpace(firSpAtkRaw) ? "0" : firSpAtkRaw;
                    if (int.TryParse(firSpAtkNormalized, out int firSpAtk) && firSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelFir"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");
                    }
                    break;
                case Types.Venom:
                    string venSpAtkRaw = attackerLabel.skills.GetValues(Types.Venom).spAtk;
                    string venSpAtkNormalized = string.IsNullOrWhiteSpace(venSpAtkRaw) ? "0" : venSpAtkRaw;
                    if (int.TryParse(venSpAtkNormalized, out int venSpAtk) && venSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelVnm"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");
                    }
                    break;
                case Types.Possession:
                    string posSpAtkRaw = attackerLabel.skills.GetValues(Types.Possession).spAtk;
                    string posSpAtkNormalized = string.IsNullOrWhiteSpace(posSpAtkRaw) ? "0" : posSpAtkRaw;
                    if (int.TryParse(posSpAtkNormalized, out int posSpAtk) && posSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPsn"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");
                    }
                    break;
                case Types.Electricity:
                    string eleSpAtkRaw = attackerLabel.skills.GetValues(Types.Electricity).spAtk;
                    string eleSpAtkNormalized = string.IsNullOrWhiteSpace(eleSpAtkRaw) ? "0" : eleSpAtkRaw;
                    if (int.TryParse(eleSpAtkNormalized, out int eleSpAtk) && eleSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEle"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");

                    }
                    break;
                case Types.Acid:
                    string aciSpAtkRaw = attackerLabel.skills.GetValues(Types.Acid).spAtk;
                    string aciSpAtkNormalized = string.IsNullOrWhiteSpace(aciSpAtkRaw) ? "0" : aciSpAtkRaw;
                    if (int.TryParse(aciSpAtkNormalized, out int aciSpAtk) && aciSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelAcd"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");
                    }
                    break;
                case Types.Frost:
                    string froSpAtkRaw = attackerLabel.skills.GetValues(Types.Frost).spAtk;
                    string froSpAtkNormalized = string.IsNullOrWhiteSpace(froSpAtkRaw) ? "0" : froSpAtkRaw;
                    if (int.TryParse(froSpAtkNormalized, out int froSpAtk) && froSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelFst"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");
                    }
                    break;
                default:
                    dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEnemy");
                    break;
            }

            StartCoroutine(reciverCharacter.GetComponent<PlayerInterface>().GotHitted()); // plays the hitted animation
        }
        else // otherwise it gets the enemy GameObject
        {
            int index = Array.IndexOf(eC, reciver); // gets the index of the enemy that received the damage

            reciverCharacter = enemiesInterface[index]; // gets the enemy GameObject

            index = Array.IndexOf(playerSelected, true); // gets the index of the selected player

            attackerCharacter = playersInterface[index]; // gets the player GameObject

            BattleCharacter attackerLabel = GetPlayerCharacter(index);

            switch (type)  // If any spkAtk is 0 the dmgLabel doesnt display the type icon
            {
                case Types.Strength:
                    string strSpAtkRaw = attackerLabel.skills.GetValues(Types.Strength).spAtk;
                    string strSpAtkNormalized = string.IsNullOrWhiteSpace(strSpAtkRaw) ? "0" : strSpAtkRaw;
                    if (int.TryParse(strSpAtkNormalized, out int strSpAtk2) && strSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPhy");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelStr"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Drop Shadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Magic:
                    string magSpAtkRaw = attackerLabel.skills.GetValues(Types.Magic).spAtk;
                    string magSpAtkNormalized = string.IsNullOrWhiteSpace(magSpAtkRaw) ? "0" : magSpAtkRaw;
                    if (int.TryParse(magSpAtkNormalized, out int magSpAtk2) && magSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPhy");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelMgc"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Drop Shadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Fire:
                    string firSpAtkRaw = attackerLabel.skills.GetValues(Types.Fire).spAtk;
                    string firSpAtkNormalized = string.IsNullOrWhiteSpace(firSpAtkRaw) ? "0" : firSpAtkRaw;
                    if (int.TryParse(firSpAtkNormalized, out int firSpAtk2) && firSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPhy");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelFir"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Drop Shadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Venom:
                    string venSpAtkRaw = attackerLabel.skills.GetValues(Types.Venom).spAtk;
                    string venSpAtkNormalized = string.IsNullOrWhiteSpace(venSpAtkRaw) ? "0" : venSpAtkRaw;

                    if (int.TryParse(venSpAtkNormalized, out int venSpAtk2) && venSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPhy");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelVnm"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Drop Shadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Possession:
                    string spAtkRaw = attackerLabel.skills.GetValues(Types.Possession).spAtk;
                    string spAtkNormalized = string.IsNullOrWhiteSpace(spAtkRaw) ? "0" : spAtkRaw;

                    if (int.TryParse(spAtkNormalized, out int posSpAtk2) && posSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPhy");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPsn");
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Drop Shadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Electricity:
                    string eleSpAtkRaw = attackerLabel.skills.GetValues(Types.Electricity).spAtk;
                    string eleSpAtkNormalized = string.IsNullOrWhiteSpace(eleSpAtkRaw) ? "0" : eleSpAtkRaw;

                    if (int.TryParse(eleSpAtkNormalized, out int eleSpAtk2) && eleSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPhy");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelEle"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Drop Shadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Acid:
                    string aciSpAtkRaw = attackerLabel.skills.GetValues(Types.Acid).spAtk;
                    string aciSpAtkNormalized = string.IsNullOrWhiteSpace(aciSpAtkRaw) ? "0" : aciSpAtkRaw;
                    if (int.TryParse(aciSpAtkNormalized, out int aciSpAtk2) && aciSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPhy");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelAcd"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Drop Shadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Frost:
                    string froSpAtkRaw = attackerLabel.skills.GetValues(Types.Frost).spAtk;
                    string froSpAtkNormalized = string.IsNullOrWhiteSpace(froSpAtkRaw) ? "0" : froSpAtkRaw;
                    if (int.TryParse(froSpAtkNormalized, out int froSpAtk2) && froSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPhy");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelFst"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Drop Shadow");
                        physicalAttack = false;
                    }
                    break;
                default:
                    dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelPhy");
                    physicalAttack = true;
                    break;
            }

            if (repelled) // plays the hitted animation
            {
                StartCoroutine(attackerCharacter.GetComponent<PlayerInterface>().GotHitted());
                StartCoroutine(grid.DestroyRunes());
            }
        }
        if (missed)
        {
            if (reciver.isEnemy) enemyActions = Mathf.Min(4, enemyActions + 1);
            else playerActions = Mathf.Min(4, playerActions + 1);
        }

        if ((critical || weak || superEffective) && !missed) // if the damage is critical, it doesn't let the character enter the cooldown
        {
            int extraActions = (critical ? 1 : 0) + (weak || superEffective ? 1 : 0);
            if (reciver.isEnemy) { attackerCharacter.GetComponent<PlayerInterface>().didCrit = true; playerActions = Mathf.Min(4 - playerEmptyActions, extraActions + playerActions); }
            else { attackerCharacter.GetComponent<EnemyInterface>().didCrit = true; enemyActions = Mathf.Min(4 - enemyEmptyActions, extraActions + enemyActions); }
        }

        if (absorbed || repelled || blocked || parried)
        {
            absorbedAttack = absorbed;
            repelledAttack = repelled;
            blockedAttack = blocked;
            parriedAttack = parried;
            dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DmgLabelSpec"); // if the damage was absorbed, repelled or blocked, it uses a different prefab
        }

        if (counter) // show counter label if counter
        {
            counterLabel = counter;
            dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/CounterLabel");
            if (reciver.isEnemy) dmgLabelPrefab.transform.GetChild(1).GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Player");
            else dmgLabelPrefab.transform.GetChild(1).GetComponent<TextMeshProUGUI>().fontMaterial = Resources.Load<Material>("Materials/FOT-NewRodin Pro Enemy");
        }

        // 1. Load and instantiate the label
        GameObject dmgLabel = Instantiate(dmgLabelPrefab, canvas.transform);

        // 2. Convert world position to screen point
        Vector3 screenPos = mainCamera.WorldToScreenPoint(reciverCharacter.transform.position);

        // 3. Convert screen position to canvas local position
        RectTransform canvasRect = canvas.GetComponent<RectTransform>();
        Vector2 anchoredPos;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(canvasRect, screenPos, mainCamera, out anchoredPos);

        // 4. Set the UI element's position
        RectTransform labelRect = dmgLabel.GetComponent<RectTransform>();
        labelRect.anchoredPosition = anchoredPos;

        // 5. Set label random position
        if (reciver.isEnemy)
        {
            labelRect.anchoredPosition += new Vector2(
                UnityEngine.Random.Range(-400f, 100f),
                UnityEngine.Random.Range(-50f, 250f)
            );
        }

        GameObject dmgStar = Instantiate(dmgStarPrefab, attackerCharacter.transform.position, Quaternion.identity); // creates the damage star
        GameObject hitEffectPrefab = Resources.Load<GameObject>("Prefabs/HitEffect"); // loads the hit effect prefab

        Instantiate(hitEffectPrefab, reciverCharacter.transform.position - new Vector3(0, 0, 0.5f), Quaternion.identity); // creates the hit effect

        StartCoroutine(dmgStar.GetComponent<DamageStar>().MoveObject(reciverCharacter.transform.position)); // makes the damage star move

        if (reciver.isStunned && reciver.isEnemy && reciver.canReceiveCoins)
        {
            // 1. Load and instantiate the label
            GameObject coinLabel = Instantiate(Resources.Load<GameObject>("Prefabs/DmgLabelCoin"),canvas.transform); // creates the damage label

            // 2. Set the UI element's position
            RectTransform coinlabelRect = coinLabel.GetComponent<RectTransform>();
            coinlabelRect.anchoredPosition = anchoredPos;

            // 3. Set coin label random position
            coinlabelRect.anchoredPosition += new Vector2(
                UnityEngine.Random.Range(-400f, 100f),
                UnityEngine.Random.Range(-50f, 250f)
            );

            int amountGained = GoldenStrike[Mathf.Min(grid.combo, 6)];
            coinLabel.GetComponent<TextMeshProUGUI>().text = "+ " + amountGained + " Gold";
            money += amountGained;
        }

        damageNumber = amount;

        // sets the text of the damage label            // Formats numbers 1000 to 1.000
        string amountFormatted = amount.ToString("N0", new CultureInfo("de-DE"));
        string damageText = missed ? "MISSED" : "-" + amountFormatted;
        if (!missed && !absorbed && !repelled && !blocked && !parried) // if the damage was not missed, absorbed, repelled, blocked or parried
        {
            if (pIndex == 0 && !reciver.isEnemy) // makes sure the label doesnt go offscreen
            {
                if (critical) damageText += "\n<size=50>CRIT</size>";
                if (weak) damageText += "\n<size=50>WEAK</size>";
                if (superEffective) damageText += "\n<size=50>SUPER EFFECTIVE</size>";
                if (notEffective) damageText += "\n<size=50>NOT EFFECTIVE</size>";
            }
            else if (pIndex == 3 && !reciver.isEnemy) // // makes sure the label doesnt go offscreen
            {
                if (critical) damageText += "\n<size=50>CRIT</size>";
                if (weak) damageText += "\n<size=50>WEAK</size>";
                if (superEffective) damageText += "\n<size=50>SUPER EFFECTIVE</size>";
                if (notEffective) damageText += "\n<size=50>NOT EFFECTIVE</size>";
            }
            else
            {
                if (critical) damageText += "\n<size=50>CRIT</size>";
                if (weak) damageText += "\n<size=50>WEAK</size>";
                if (superEffective) damageText += "\n<size=50>SUPER EFFECTIVE</size>";
                if (notEffective) damageText += "\n<size=50>NOT EFFECTIVE</size>";
            }
        }
        else if (absorbed && !missed) damageText = "ABSORBED";
        else if (repelled && !missed) damageText = "REPELLED";
        else if (blocked && !missed) damageText = "BLOCKED";
        else if (parried && !missed) damageText = "PARRY";


        dmgLabel.GetComponent<TextMeshProUGUI>().text = damageText;

        dmgLabel.GetComponent<TextMeshProUGUI>().fontStyle = FontStyles.Bold; // makes the text bold

        return true;
    }

    //**************************//
    //**************************//
    //****** Party System ******//
    //**************************//
    //**************************//

    public float CalculatePartyAverageLevel()
    {
        float totalMembers = partyCharacters[partySelected].activeCharacters.Count(ac => ac != null);
        float totalLevels = partyCharacters[partySelected].activeCharacters.Where(ac => ac != null).Sum(ac => ac.level);

        return totalLevels / Mathf.Max(1, totalMembers);
    }

    public bool IsCharacterInActive(BattleCharacter character) => pC.FirstOrDefault(pc => pc == character) != null;


    public void HealDeadCharacters(float percentage)
    {
        foreach (var character in partyCharacters[partySelected].stockCharacters) if(character != null && character.IsDead) character.Heal((int)(character.maxHP * (percentage / 100)) + 1);
    }

    public void HealSingleCharacter(float percentage, int index)
    {
        BattleCharacter character = pC[index];
        if (character != null && character.IsDead) character.Heal((int)(character.maxHP * (percentage / 100)) + 1);
    }

    // gets the party characters by index
    public PartyCharacters GetPartyCharacters(int index)
    {
        // Safety check: ensure index is valid and party exists
        if (index < 0 || index >= partyCharacters.Count)
        {
            return null;
        }
        return partyCharacters[index];
    }

    // gets the stock character in the selected party by index
    public BattleCharacter GetStockCharacter(int index)
    {
        if (partySelected == -1) return null;

        return partyCharacters[partySelected].stockCharacters[index];
    }

    // sets the character to the party
    public void SetCharacterToParty(int partyIndex, int slotIndex, bool isActive, BattleCharacter character)
    {
        if (isActive) partyCharacters[partyIndex].activeCharacters[slotIndex] = character;
        else partyCharacters[partyIndex].stockCharacters[slotIndex] = character;

        // Save the updated party configuration to JSON files
        SaveParties();
    }

    // sets the party name
    public void SetPartyName(int index, string name) => partyCharacters[index].name = name;

    // sets the selected party
    public void SetSelectedParty(int index)
    {
        pC = index != -1 ? partyCharacters[index].activeCharacters : new BattleCharacter[4];

        if (index != partySelected)
            SetEnergyTime(0);

        partySelected = index;
    }

    // checks if the character is on the party
    public bool IsCharacterOnParty(BattleCharacter character, int index)
    {
        // Safety check: ensure index is valid and party exists
        if (index < 0 || index >= partyCharacters.Count || partyCharacters[index] == null)
        {
            return false;
        }

        return partyCharacters[index].activeCharacters.Contains(character) || partyCharacters[index].stockCharacters.Contains(character);
    }


    //**************************//
    //**************************//
    //**** Character System ****//
    //**************************//
    //**************************//

    public float CalculateAverageLevel()
    {
        float totalCharacters = eC.Count(ec => ec != null) + partyCharacters[partySelected].stockCharacters.Count(sc => sc != null);
        float totalLevels = eC.Where(ec => ec != null).Sum(ec => ec.level) + partyCharacters[partySelected].stockCharacters.Where(sc => sc != null).Sum(sc => sc.level);

        return totalLevels / totalCharacters;
    }

    public bool IsThereCharacters() // returns if there are players and enemies, if not anything related to damage and turns is not executed
    {
        bool isTherePlayers = Array.FindIndex(pC, pc => pc != null && !pc.IsDead) != -1; // check if there are players by finding the index of the first player that isn't dead or null

        bool isThereEnemies = Array.FindIndex(eC, ec => ec != null && !ec.IsDead) != -1; // same thing but for the enemies

        return isTherePlayers && isThereEnemies;

    }

    public int AddCharacter() // adds a new character
    {
        string id = (characters.Count < 1) ? "0" : (int.Parse(characters[characters.Count - 1].id) + 1).ToString(); // if the list of characters is empty, the id is "0", if not, the id is the last character's id + 1

        // Ensure default lists are not empty, create defaults if needed
        if (defaultStats.Count == 0)
        {
            defaultStats.Add(new CharacterStatus());
        }
        if (defaultSkills.Count == 0)
        {
            defaultSkills.Add(new CharacterSkills());
        }
        if (defaultMods.Count == 0)
        {
            defaultMods.Add(new CharacterMods());
        }

        // Create new random objects instead of reusing templates to ensure randomization
        CharacterStatus cSt = new CharacterStatus(); // Creates random HP, ATK, DEF values
        CharacterSkills cSk = new CharacterSkills(); // Creates random skill values 0-15 for each type
        CharacterMods cMo = new CharacterMods(); // Creates random mod values 1-100

        BattleCharacter character = new(id, cSt, cSk, cMo); // creates a new battle character

        characters.Add(character); // adds the character to the list

        // Update character mapping and mark as dirty for incremental saving
        CharacterChangeTracker.Instance.UpdateCharacterMapping(characters);
        CharacterChangeTracker.Instance.MarkCharacterDirty(character.id);

        // Save the updated character list and default lists to JSON files
        SaveCharacters(); // This handles both LoadedValues update and chunked/standard saving

        return characters.Count - 1; // returns the index
    }

    public void RemoveCharacter(BattleCharacter character) // removes the character
    {
        characters.Remove(character);

        // Character removal requires full save due to index changes
        CharacterChangeTracker.Instance.UpdateCharacterMapping(characters);
        CharacterChangeTracker.Instance.RequireFullSave();

        // Save the updated character list to JSON files
        SaveCharacters(); // This handles both LoadedValues update and chunked/standard saving
    }

    public int GetCharacter(BattleCharacter chararacter) => characters.IndexOf(chararacter); // gets the index of the character

    public BattleCharacter GetCharacter(int index) => characters[index]; // gets the character at the index

    public List<BattleCharacter> GetCharacters() => characters; // Gets all the characters

    public BattleCharacter GetCharacterByID(string id) => characters.Find(c => c.id == id); // gets the character by id

    public void SetCharacter(BattleCharacter character, int index) // sets the character at the index
    {
        characters[index] = character;

        // Mark character as dirty for incremental saving
        CharacterChangeTracker.Instance.MarkCharacterDirtyByIndex(index, characters);

        // Save the updated character list to JSON files
        SaveCharacters(); // This handles both LoadedValues update and chunked/standard saving
    }


    //***************************//
    //***************************//
    //****** Player System ******//
    //***************************//
    //***************************//

    public bool CantPlayerPlay()
    {
        bool isThereAvalables = partyCharacters[partySelected].stockCharacters.All(c => (c?.IsDead ?? true) || (c?.isStunned ?? true));
        return pC.All(pc => (pc?.isStunned ?? isThereAvalables) || (pc?.IsDead ?? isThereAvalables));
    }

    public float CalculateAverageActivePartySpeed()
    {
        float totalCharacters = pC.Count(pc => pc != null);
        float totalSpeed = pC.Where(pc => pc != null).Sum(pc => pc.mods.GetSpeed());

        return totalSpeed / totalCharacters;
    }

    // checks if the selected player is avalable
    public bool IsSelectedPlayerAvalable() => pC[Array.IndexOf(playerSelected, true)] != null && !pC[Array.IndexOf(playerSelected, true)].IsDead && playersInterface[GetSelectedPlayer()].GetComponent<PlayerInterface>().AttackCooldown.fillAmount <= 0;

    public bool IsThereAnyPlayerNotInCooldown()
    {
        bool isItThere = false;

        for (int i = 0; i < pC.Length; i++)
        {
            PlayerInterface player = playersInterface[i].GetComponent<PlayerInterface>();
            if (player.AttackCooldown.fillAmount <= 0.0001f && pC[i] != null && !pC[i].IsDead && !pC[i].isStunned)
            {
                isItThere = true;
                if (playersInterface[GetSelectedPlayer()].GetComponent<PlayerInterface>().AttackCooldown.fillAmount > 0.0001f) SetPlayerSelected(i);
                break;
            }
        }

        return isItThere;
    }

    public void RemovePlayer() => pC[Array.IndexOf(playerSelected, true)] = null; // removes the player

    public BattleCharacter GetPlayerCharacter(int index) => pC[index]; // gets the player character at the index

    public int GetPlayerIndex(BattleCharacter character) => Array.IndexOf(pC, character); // gets the index of the player

    public int GetSelectedPlayer() => Array.IndexOf(playerSelected, true);

    public void SetPlayerCharacter(int index) // sets the player character to the selected player slot
    {
        int selectedPlayer = Array.IndexOf(playerSelected, true);

        int selectedParty = CharactersForParty.GetComponent<CharatersForPartyUIHandler>().selectedParty;

        partyCharacters[selectedParty].activeCharacters[selectedPlayer] = partyCharacters[selectedParty].stockCharacters[index];

        SetSelectedParty(selectedParty);

        playersInterface[selectedPlayer].GetComponent<PlayerInterface>().didCrit = true;

        playerActions--;
        playerEmptyActions++;
        if (playerActions == 0)
        {
            playerTurn = false;
            StartCoroutine(grid.SwitchTurns());
        }
    }

    public bool GetPlayerSelected(int index) => playerSelected[index]; // checks if the player is selected

    public void SetPlayerSelected(int index) // sets the player selected
    {
        playerSelected = new bool[playerSelected.Length];

        playerSelected[index] = true;
    }

    public bool IsAnyPlayerSelected()
    {
        for (int i = 0; i < pC.Length; i++)
        {
            if (pC[i] != null && !pC[i].IsDead && playerSelected[i])
                return true;
        }
        return false;
    }

    public bool AreAllActivePlayersDead()
    {
        if (partyCharacters == null || partySelected < 0 || partySelected >= partyCharacters.Count)
            return false; // Invalid party = assume not all dead

        var active = partyCharacters[partySelected]?.activeCharacters;
        if (active == null || active.Length == 0)
            return false; // No active characters = can't say they're all dead

        return active.All(c => c == null || c.IsDead);
    }


    //**************************//
    //**************************//
    //****** Enemy System ******//
    //**************************//
    //**************************//

    public float CalculateAverageEnemyLevel()
    {
        float totalEnemies = eC.Count(ec => ec != null);
        float totalLevels = eC.Where(ec => ec != null).Sum(ec => ec.level);

        return totalLevels / totalEnemies;
    }

    public bool IsThereAnyEnemyNotInCooldown() // checks if there is any enemy not in cooldown
    {
        for (int i = 0; i < eC.Length; i++) // loops through the enemies
        {
            EnemyInterface enemy = enemiesInterface[i].GetComponent<EnemyInterface>(); // gets the enemy
            if (enemy.AttackCooldown.fillAmount <= 0.0001f && eC[i] != null && !eC[i].IsDead) // checks if that enemy isn't in cooldown, is alive and isn't null
            {
                // changes the selected enemy to the one that isn't in cooldown if the current selected enemy is in cooldown
                if (GetSelectedEnemy() != -1 && enemiesInterface[GetSelectedEnemy()].GetComponent<EnemyInterface>().AttackCooldown.fillAmount > 0.0001f) SetEnemySelected(i);
                return true;
            }
        }

        return false;
    }

    private IEnumerator GetAllEnemies()
    {
        while (!stopGetAllEnemies)
        {
            List<BattleCharacter> tempEnemies = new();
            for (int i = 0; i < characters.Count; i++)
            {
                if (characters[i].isEnemy) tempEnemies.Add(characters[i]);

                if (i % 10 == 0) yield return new WaitForSeconds(0);
            }
            yield return null;
            enemies = tempEnemies;
        }
    }

    public void StopGetAllEnemies()
    {
        stopGetAllEnemies = true;
    }

    public int GetNumberOfEnemies() => Mathf.Min(4, ChangeOnlyNulls.GetComponent<Toggle>().isOn ? eC.Count(ec => ec != null && !ec.IsDead) : characters.Count(c => c != null && c.isEnemy && !c.IsDead));

    public BattleCharacter GetEnemyCharacter(int index) => eC[index]; // gets the enemy character at the index

    public int GetSelectedEnemy() => Array.IndexOf(enemySelected, true); // gets the selected enemy

    public IEnumerator SetEnemyCharacter(BattleCharacter character, bool changeOnlyNulls = true) // sets the enemy character
    {
        if (Array.IndexOf(eC, eC.FirstOrDefault(ec => ec != null && ec.IsDead)) != -1 && !changeOnlyNulls) while (!grid.playerTurn || enemyTurns != playerTurns) yield return null;

        int index = Array.IndexOf(eC, eC.FirstOrDefault(ec => ec == null || (ec.IsDead && !changeOnlyNulls)));
        if (index != -1 && Array.IndexOf(eC, character) == -1)
        {
            if (eC[index] != null && eC[index].IsDead && !changeOnlyNulls) enemiesInterface[index].GetComponent<EnemyAnimation>().respawnOffset += new Vector3(0, 2, 0);
            eC[index] = character;
        }
    }

    public bool GetEnemySelected(int index) => enemySelected[index]; // checks if the enemy is selected

    public void SetEnemySelected(int index) // sets the enemy selected
    {
        bool curentSelecedState = enemySelected[index];

        enemySelected = new bool[enemySelected.Length];

        enemySelected[index] = !curentSelecedState; // sets the enemy selected
    }

    public bool AreAllEnemiesDead()
    {
        return eC.All(ec => ec == null || ec.IsDead);
    }


    //*************************//
    //*************************//
    //****** Save System ******//
    //*************************//
    //*************************//

    // Save the values if the reset button is pressed
    public void SaveParties() => LoadedValues.SaveParties(partyCharacters);

    public void SaveCharacters() => LoadedValues.SaveCharacters(characters);


    /// <summary>
    /// Marks a character as modified for incremental saving
    /// Call this whenever a character's data is changed outside of SetCharacter/AddCharacter/RemoveCharacter
    /// </summary>
    public void MarkCharacterModified(BattleCharacter character)
    {
        if (character != null && !string.IsNullOrEmpty(character.id))
        {
            CharacterChangeTracker.Instance.MarkCharacterDirty(character.id);
        }
    }

    /// <summary>
    /// Marks a character as modified by index for incremental saving
    /// </summary>
    public void MarkCharacterModified(int characterIndex)
    {
        if (characterIndex >= 0 && characterIndex < characters.Count)
        {
            CharacterChangeTracker.Instance.MarkCharacterDirtyByIndex(characterIndex, characters);
        }
    }

    public void SaveValues() => LoadedValues.SaveValues(B, C, D, E, F, G, Difficulty, StockWeight, MaxPP, ReductionPerCombo, FractionOfAttacksPerAction, ModFraction, IDBOffset, infiniteToggle.GetComponent<Toggle>().isOn, ShowHP.GetComponent<Toggle>().isOn, ChangeOnlyNulls.GetComponent<Toggle>().isOn);

    public void SaveEnergyTimer() => LoadedValues.SaveEnergyTimer(energyTimer);


    //***************************//
    //***************************//
    //****** Other Systems ******//
    //***************************//
    //***************************//

    // resets the energy amount
    public void SetEnergyTime(int amount = 0)
    {
        if (amount <= 0) amount = energyTimer;
        energyLeft = amount;
    }

    public bool IsEnergyLeft() => energyLeft > 0; // checks if there is energy left

    public bool IsPPLeft() => ppLeft > 0; // checks if there is power left

    // Counts down the energy
    public IEnumerator CountDown(Rune rune)
    {
        if (canCountdown) yield break;
        canCountdown = true;
        SetEnergyTime();
        while (energyLeft > 0 && canCountdown) // while there is energy, reduces it with deltaTime
        {
            energyLeft -= Time.deltaTime;
            yield return null;
        }

        if (energyLeft < 0)
        {
            rune.visualImage.color = new Color(1f, 1f, 1f, 1f);
            rune.visual.localPosition = Vector3.zero;
            RuneOffVisual.Instance.Hide();
            grid.HandleMatchesAfterMove();
            playerTurn = false;
            StartCoroutine(grid.SwitchTurns());
        }
    }

    public int ChanceOfEscape() // calculates the chance of escape
    {
        List<BattleCharacter> party = GetPartyCharacters(partySelected).activeCharacters.Concat(GetPartyCharacters(partySelected).stockCharacters).ToList(); // gets the party

        int numOfPlayers = party.Count(pc => pc != null && !pc.IsDead), numOfEnemies = eC.Count(ec => ec != null && !ec.IsDead); // gets the number of players and enemies

        float spdParty = 0, lukParty = 0, spdEnemies = 0, lukEnemies = 0, partyMed = 0, enemiesMed = 0; // creates the variables

        foreach (var player in party) if (player != null && !player.IsDead) { spdParty += player.mods.GetSpeed(); lukParty += player.mods.GetLuck(); } // gets the total speed and luck of the players
        foreach (var enemy in eC) if (enemy != null && !enemy.IsDead) { spdEnemies += enemy.mods.GetSpeed(); lukEnemies += enemy.mods.GetLuck(); } // same thing but for the enemies

        if (numOfPlayers > 0) partyMed = spdParty / (float)numOfPlayers * (lukParty / (float)numOfPlayers); // calculates the median of the party
        if (numOfEnemies > 0) enemiesMed = spdEnemies / (float)numOfEnemies * (lukEnemies / (float)numOfEnemies); // same thing but for the enemies

        int chance = (int)(partyMed / (partyMed + enemiesMed) * 100); // calculates the chance

        // makes sure the chance is between 0 and 100
        if (chance > 100) return 100;
        if (chance < 0) return 0;

        return chance; // otherwise returns the chance
    }

    public void TryToEscape() // tries to escape
    {
        // gets a random number between 0 and 100 and checks if it is less than the chance
        if (UnityEngine.Random.Range(0, 100) < ChanceOfEscape()) new RestartGame(); // if it is, it restarts the game
        else // otherwise shows the escape fail popup and switches the turns
        {
            EscapeFailPopup.SetActive(true);
            playerActions = 1;

            playerTurn = false;
            StartCoroutine(grid.SwitchTurns());
        }
    }

    public void UpdateComboChance(int index, int value) // updates the combo chance at the index
    {
        ComboChance[index] = value;
        LoadedValues.SaveComboChance(ComboChance);
    }

    public void UpdateGoldenStrike(int index, int value) // updates the combo chance at the index
    {
        GoldenStrike[index] = value;
        LoadedValues.SaveGoldenStrike(GoldenStrike);
    }

    public IEnumerator ScreenShake(float strength, float duration) // screen shakes
    {
        duration /= 60f; // makes the duration in seconds, using 60 fps as a reference
        float timer = 0;
        while (timer < duration)
        {
            timer += Time.deltaTime;
            Vector3 shakePos = new(UnityEngine.Random.Range(-strength, strength), UnityEngine.Random.Range(-strength, strength), 0); // calculates the shake offset with a random range of -strength to strength

            GameObject.Find("BackGround").transform.localPosition = new Vector3(0f, 0f, 400f) + shakePos * 2f; // moves the background

            for (int i = 0; i < eC.Length; i++) enemiesInterface[i].GetComponent<EnemyAnimation>().offset = shakePos / 20f; // moves the enemies
            yield return null;
        }
        GameObject.Find("BackGround").transform.localPosition = new Vector3(0f, 0f, 400f); // resets the background position
        for (int i = 0; i < eC.Length; i++) enemiesInterface[i].GetComponent<EnemyAnimation>().offset = new(); // resets the enemy offset
    }
}
