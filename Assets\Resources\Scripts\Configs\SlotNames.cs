using UnityEngine;
using UnityEngine.UI;

public class SlotNames : MonoBehaviour
{

    public GameObject player0Name, player1Name, player2Name, player3Name;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        gameObject.GetComponent<Toggle>().onValueChanged.AddListener(delegate { ShowOnHideNames(); });
    }

    // Update is called once per frame
    void Update()
    {

    }

    void ShowOnHideNames()
    {
        if (player0Name.activeSelf) player0Name.SetActive(false); else player0Name.SetActive(true);
        if (player1Name.activeSelf) player1Name.SetActive(false); else player1Name.SetActive(true);
        if (player2Name.activeSelf) player2Name.SetActive(false); else player2Name.SetActive(true);
        if (player3Name.activeSelf) player3Name.SetActive(false); else player3Name.SetActive(true);
    }
}
